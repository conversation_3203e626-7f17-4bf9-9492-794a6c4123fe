/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/uploadthing/route";
exports.ids = ["app/api/uploadthing/route"];
exports.modules = {

/***/ "(rsc)/./app/api/uploadthing/core.ts":
/*!*************************************!*\
  !*** ./app/api/uploadthing/core.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uploadRouter: () => (/* binding */ uploadRouter)\n/* harmony export */ });\n/* harmony import */ var uploadthing_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uploadthing/next */ \"(rsc)/./node_modules/uploadthing/next/index.js\");\n\nconst f = (0,uploadthing_next__WEBPACK_IMPORTED_MODULE_0__.createUploadthing)();\nconst uploadRouter = {\n    // Property image uploader\n    propertyImageUploader: f({\n        image: {\n            maxFileSize: \"8MB\",\n            maxFileCount: 10\n        }\n    }).middleware(async ({ req })=>{\n        // You can add authentication here\n        return {\n            uploadedBy: \"property-system\"\n        };\n    }).onUploadComplete(async ({ metadata, file })=>{\n        console.log(\"Upload complete for userId:\", metadata.uploadedBy);\n        console.log(\"file url\", file.url);\n        return {\n            uploadedBy: metadata.uploadedBy\n        };\n    }),\n    // Avatar uploader\n    avatarUploader: f({\n        image: {\n            maxFileSize: \"4MB\",\n            maxFileCount: 1\n        }\n    }).middleware(async ({ req })=>{\n        return {\n            uploadedBy: \"user-system\"\n        };\n    }).onUploadComplete(async ({ metadata, file })=>{\n        console.log(\"Upload complete for userId:\", metadata.uploadedBy);\n        console.log(\"file url\", file.url);\n        return {\n            uploadedBy: metadata.uploadedBy\n        };\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/uploadthing/core.ts\n");

/***/ }),

/***/ "(rsc)/./app/api/uploadthing/route.ts":
/*!**************************************!*\
  !*** ./app/api/uploadthing/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var uploadthing_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uploadthing/next */ \"(rsc)/./node_modules/uploadthing/next/index.js\");\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core */ \"(rsc)/./app/api/uploadthing/core.ts\");\n\n\n// Export routes for Next App Router\nconst { GET, POST } = (0,uploadthing_next__WEBPACK_IMPORTED_MODULE_1__.createRouteHandler)({\n    router: _core__WEBPACK_IMPORTED_MODULE_0__.uploadRouter\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL3VwbG9hZHRoaW5nL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBc0Q7QUFDaEI7QUFFdEMsb0NBQW9DO0FBQzdCLE1BQU0sRUFBRUUsR0FBRyxFQUFFQyxJQUFJLEVBQUUsR0FBR0gsb0VBQWtCQSxDQUFDO0lBQzlDSSxRQUFRSCwrQ0FBWUE7QUFDdEIsR0FBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBaG1lZFxcRGVza3RvcFxcY29kZVxcYm9vdFxcZGFzaGJvYXJkXFxhcHBcXGFwaVxcdXBsb2FkdGhpbmdcXHJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVJvdXRlSGFuZGxlciB9IGZyb20gXCJ1cGxvYWR0aGluZy9uZXh0XCI7XG5pbXBvcnQgeyB1cGxvYWRSb3V0ZXIgfSBmcm9tIFwiLi9jb3JlXCI7XG5cbi8vIEV4cG9ydCByb3V0ZXMgZm9yIE5leHQgQXBwIFJvdXRlclxuZXhwb3J0IGNvbnN0IHsgR0VULCBQT1NUIH0gPSBjcmVhdGVSb3V0ZUhhbmRsZXIoe1xuICByb3V0ZXI6IHVwbG9hZFJvdXRlcixcbn0pO1xuIl0sIm5hbWVzIjpbImNyZWF0ZVJvdXRlSGFuZGxlciIsInVwbG9hZFJvdXRlciIsIkdFVCIsIlBPU1QiLCJyb3V0ZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/uploadthing/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuploadthing%2Froute&page=%2Fapi%2Fuploadthing%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuploadthing%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDesktop%5Ccode%5Cboot%5Cdashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDesktop%5Ccode%5Cboot%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuploadthing%2Froute&page=%2Fapi%2Fuploadthing%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuploadthing%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDesktop%5Ccode%5Cboot%5Cdashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDesktop%5Ccode%5Cboot%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Ahmed_Desktop_code_boot_dashboard_app_api_uploadthing_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/uploadthing/route.ts */ \"(rsc)/./app/api/uploadthing/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/uploadthing/route\",\n        pathname: \"/api/uploadthing\",\n        filename: \"route\",\n        bundlePath: \"app/api/uploadthing/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\api\\\\uploadthing\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Ahmed_Desktop_code_boot_dashboard_app_api_uploadthing_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuploadthing%2Froute&page=%2Fapi%2Fuploadthing%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuploadthing%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDesktop%5Ccode%5Cboot%5Cdashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDesktop%5Ccode%5Cboot%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/effect","vendor-chunks/fast-check","vendor-chunks/@effect","vendor-chunks/pure-rand","vendor-chunks/uploadthing","vendor-chunks/@uploadthing","vendor-chunks/multipasta","vendor-chunks/find-my-way-ts","vendor-chunks/sqids"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuploadthing%2Froute&page=%2Fapi%2Fuploadthing%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuploadthing%2Froute.ts&appDir=C%3A%5CUsers%5CAhmed%5CDesktop%5Ccode%5Cboot%5Cdashboard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAhmed%5CDesktop%5Ccode%5Cboot%5Cdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();