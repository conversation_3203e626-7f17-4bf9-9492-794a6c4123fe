"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@uploadthing";
exports.ids = ["vendor-chunks/@uploadthing"];
exports.modules = {

/***/ "(rsc)/./node_modules/@uploadthing/mime-types/application/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@uploadthing/mime-types/application/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   application: () => (/* binding */ application)\n/* harmony export */ });\nconst application = {\n    \"application/andrew-inset\": {\n        source: \"iana\",\n        extensions: [\n            \"ez\"\n        ]\n    },\n    \"application/applixware\": {\n        source: \"apache\",\n        extensions: [\n            \"aw\"\n        ]\n    },\n    \"application/atom+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"atom\"\n        ]\n    },\n    \"application/atomcat+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"atomcat\"\n        ]\n    },\n    \"application/atomdeleted+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"atomdeleted\"\n        ]\n    },\n    \"application/atomsvc+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"atomsvc\"\n        ]\n    },\n    \"application/atsc-dwd+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"dwd\"\n        ]\n    },\n    \"application/atsc-held+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"held\"\n        ]\n    },\n    \"application/atsc-rsat+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"rsat\"\n        ]\n    },\n    \"application/calendar+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xcs\"\n        ]\n    },\n    \"application/ccxml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"ccxml\"\n        ]\n    },\n    \"application/cdfx+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"cdfx\"\n        ]\n    },\n    \"application/cdmi-capability\": {\n        source: \"iana\",\n        extensions: [\n            \"cdmia\"\n        ]\n    },\n    \"application/cdmi-container\": {\n        source: \"iana\",\n        extensions: [\n            \"cdmic\"\n        ]\n    },\n    \"application/cdmi-domain\": {\n        source: \"iana\",\n        extensions: [\n            \"cdmid\"\n        ]\n    },\n    \"application/cdmi-object\": {\n        source: \"iana\",\n        extensions: [\n            \"cdmio\"\n        ]\n    },\n    \"application/cdmi-queue\": {\n        source: \"iana\",\n        extensions: [\n            \"cdmiq\"\n        ]\n    },\n    \"application/cpl+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"cpl\"\n        ]\n    },\n    \"application/cu-seeme\": {\n        source: \"apache\",\n        extensions: [\n            \"cu\"\n        ]\n    },\n    \"application/dash+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"mpd\"\n        ]\n    },\n    \"application/dash-patch+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"mpp\"\n        ]\n    },\n    \"application/davmount+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"davmount\"\n        ]\n    },\n    \"application/dicom\": {\n        source: \"iana\",\n        extensions: [\n            \"dcm\"\n        ]\n    },\n    \"application/docbook+xml\": {\n        source: \"apache\",\n        extensions: [\n            \"dbk\"\n        ]\n    },\n    \"application/dssc+der\": {\n        source: \"iana\",\n        extensions: [\n            \"dssc\"\n        ]\n    },\n    \"application/dssc+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xdssc\"\n        ]\n    },\n    \"application/ecmascript\": {\n        source: \"iana\",\n        extensions: [\n            \"es\",\n            \"ecma\"\n        ]\n    },\n    \"application/emma+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"emma\"\n        ]\n    },\n    \"application/emotionml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"emotionml\"\n        ]\n    },\n    \"application/epub+zip\": {\n        source: \"iana\",\n        extensions: [\n            \"epub\"\n        ]\n    },\n    \"application/exi\": {\n        source: \"iana\",\n        extensions: [\n            \"exi\"\n        ]\n    },\n    \"application/express\": {\n        source: \"iana\",\n        extensions: [\n            \"exp\"\n        ]\n    },\n    \"application/fdt+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"fdt\"\n        ]\n    },\n    \"application/font-tdpfr\": {\n        source: \"iana\",\n        extensions: [\n            \"pfr\"\n        ]\n    },\n    \"application/geo+json\": {\n        source: \"iana\",\n        extensions: [\n            \"geojson\"\n        ]\n    },\n    \"application/gml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"gml\"\n        ]\n    },\n    \"application/gpx+xml\": {\n        source: \"apache\",\n        extensions: [\n            \"gpx\"\n        ]\n    },\n    \"application/gxf\": {\n        source: \"apache\",\n        extensions: [\n            \"gxf\"\n        ]\n    },\n    \"application/gzip\": {\n        source: \"iana\",\n        extensions: [\n            \"gz\"\n        ]\n    },\n    \"application/hyperstudio\": {\n        source: \"iana\",\n        extensions: [\n            \"stk\"\n        ]\n    },\n    \"application/inkml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"ink\",\n            \"inkml\"\n        ]\n    },\n    \"application/ipfix\": {\n        source: \"iana\",\n        extensions: [\n            \"ipfix\"\n        ]\n    },\n    \"application/its+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"its\"\n        ]\n    },\n    \"application/java-archive\": {\n        source: \"apache\",\n        extensions: [\n            \"jar\",\n            \"war\",\n            \"ear\"\n        ]\n    },\n    \"application/java-serialized-object\": {\n        source: \"apache\",\n        extensions: [\n            \"ser\"\n        ]\n    },\n    \"application/java-vm\": {\n        source: \"apache\",\n        extensions: [\n            \"class\"\n        ]\n    },\n    \"application/javascript\": {\n        source: \"iana\",\n        charset: \"UTF-8\",\n        extensions: [\n            \"js\",\n            \"mjs\"\n        ]\n    },\n    \"application/json\": {\n        source: \"iana\",\n        charset: \"UTF-8\",\n        extensions: [\n            \"json\",\n            \"map\"\n        ]\n    },\n    \"application/jsonml+json\": {\n        source: \"apache\",\n        extensions: [\n            \"jsonml\"\n        ]\n    },\n    \"application/ld+json\": {\n        source: \"iana\",\n        extensions: [\n            \"jsonld\"\n        ]\n    },\n    \"application/lgr+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"lgr\"\n        ]\n    },\n    \"application/lost+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"lostxml\"\n        ]\n    },\n    \"application/mac-binhex40\": {\n        source: \"iana\",\n        extensions: [\n            \"hqx\"\n        ]\n    },\n    \"application/mac-compactpro\": {\n        source: \"apache\",\n        extensions: [\n            \"cpt\"\n        ]\n    },\n    \"application/mads+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"mads\"\n        ]\n    },\n    \"application/manifest+json\": {\n        source: \"iana\",\n        charset: \"UTF-8\",\n        extensions: [\n            \"webmanifest\"\n        ]\n    },\n    \"application/marc\": {\n        source: \"iana\",\n        extensions: [\n            \"mrc\"\n        ]\n    },\n    \"application/marcxml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"mrcx\"\n        ]\n    },\n    \"application/mathematica\": {\n        source: \"iana\",\n        extensions: [\n            \"ma\",\n            \"nb\",\n            \"mb\"\n        ]\n    },\n    \"application/mathml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"mathml\"\n        ]\n    },\n    \"application/mbox\": {\n        source: \"iana\",\n        extensions: [\n            \"mbox\"\n        ]\n    },\n    \"application/media-policy-dataset+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"mpf\"\n        ]\n    },\n    \"application/mediaservercontrol+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"mscml\"\n        ]\n    },\n    \"application/metalink+xml\": {\n        source: \"apache\",\n        extensions: [\n            \"metalink\"\n        ]\n    },\n    \"application/metalink4+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"meta4\"\n        ]\n    },\n    \"application/mets+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"mets\"\n        ]\n    },\n    \"application/mmt-aei+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"maei\"\n        ]\n    },\n    \"application/mmt-usd+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"musd\"\n        ]\n    },\n    \"application/mods+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"mods\"\n        ]\n    },\n    \"application/mp21\": {\n        source: \"iana\",\n        extensions: [\n            \"m21\",\n            \"mp21\"\n        ]\n    },\n    \"application/mp4\": {\n        source: \"iana\",\n        extensions: [\n            \"mp4s\",\n            \"m4p\"\n        ]\n    },\n    \"application/msword\": {\n        source: \"iana\",\n        extensions: [\n            \"doc\",\n            \"dot\"\n        ]\n    },\n    \"application/mxf\": {\n        source: \"iana\",\n        extensions: [\n            \"mxf\"\n        ]\n    },\n    \"application/n-quads\": {\n        source: \"iana\",\n        extensions: [\n            \"nq\"\n        ]\n    },\n    \"application/n-triples\": {\n        source: \"iana\",\n        extensions: [\n            \"nt\"\n        ]\n    },\n    \"application/node\": {\n        source: \"iana\",\n        extensions: [\n            \"cjs\"\n        ]\n    },\n    \"application/octet-stream\": {\n        source: \"iana\",\n        extensions: [\n            \"bin\",\n            \"dms\",\n            \"lrf\",\n            \"mar\",\n            \"so\",\n            \"dist\",\n            \"distz\",\n            \"pkg\",\n            \"bpk\",\n            \"dump\",\n            \"elc\",\n            \"deploy\",\n            \"exe\",\n            \"dll\",\n            \"deb\",\n            \"dmg\",\n            \"iso\",\n            \"img\",\n            \"msi\",\n            \"msp\",\n            \"msm\",\n            \"buffer\"\n        ]\n    },\n    \"application/oda\": {\n        source: \"iana\",\n        extensions: [\n            \"oda\"\n        ]\n    },\n    \"application/oebps-package+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"opf\"\n        ]\n    },\n    \"application/ogg\": {\n        source: \"iana\",\n        extensions: [\n            \"ogx\"\n        ]\n    },\n    \"application/omdoc+xml\": {\n        source: \"apache\",\n        extensions: [\n            \"omdoc\"\n        ]\n    },\n    \"application/onenote\": {\n        source: \"apache\",\n        extensions: [\n            \"onetoc\",\n            \"onetoc2\",\n            \"onetmp\",\n            \"onepkg\"\n        ]\n    },\n    \"application/oxps\": {\n        source: \"iana\",\n        extensions: [\n            \"oxps\"\n        ]\n    },\n    \"application/p2p-overlay+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"relo\"\n        ]\n    },\n    \"application/patch-ops-error+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xer\"\n        ]\n    },\n    \"application/pdf\": {\n        source: \"iana\",\n        extensions: [\n            \"pdf\"\n        ]\n    },\n    \"application/pgp-encrypted\": {\n        source: \"iana\",\n        extensions: [\n            \"pgp\"\n        ]\n    },\n    \"application/pgp-keys\": {\n        source: \"iana\",\n        extensions: [\n            \"asc\"\n        ]\n    },\n    \"application/pgp-signature\": {\n        source: \"iana\",\n        extensions: [\n            \"asc\",\n            \"sig\"\n        ]\n    },\n    \"application/pics-rules\": {\n        source: \"apache\",\n        extensions: [\n            \"prf\"\n        ]\n    },\n    \"application/pkcs10\": {\n        source: \"iana\",\n        extensions: [\n            \"p10\"\n        ]\n    },\n    \"application/pkcs7-mime\": {\n        source: \"iana\",\n        extensions: [\n            \"p7m\",\n            \"p7c\"\n        ]\n    },\n    \"application/pkcs7-signature\": {\n        source: \"iana\",\n        extensions: [\n            \"p7s\"\n        ]\n    },\n    \"application/pkcs8\": {\n        source: \"iana\",\n        extensions: [\n            \"p8\"\n        ]\n    },\n    \"application/pkix-attr-cert\": {\n        source: \"iana\",\n        extensions: [\n            \"ac\"\n        ]\n    },\n    \"application/pkix-cert\": {\n        source: \"iana\",\n        extensions: [\n            \"cer\"\n        ]\n    },\n    \"application/pkix-crl\": {\n        source: \"iana\",\n        extensions: [\n            \"crl\"\n        ]\n    },\n    \"application/pkix-pkipath\": {\n        source: \"iana\",\n        extensions: [\n            \"pkipath\"\n        ]\n    },\n    \"application/pkixcmp\": {\n        source: \"iana\",\n        extensions: [\n            \"pki\"\n        ]\n    },\n    \"application/pls+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"pls\"\n        ]\n    },\n    \"application/postscript\": {\n        source: \"iana\",\n        extensions: [\n            \"ai\",\n            \"eps\",\n            \"ps\"\n        ]\n    },\n    \"application/provenance+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"provx\"\n        ]\n    },\n    \"application/prs.cww\": {\n        source: \"iana\",\n        extensions: [\n            \"cww\"\n        ]\n    },\n    \"application/pskc+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"pskcxml\"\n        ]\n    },\n    \"application/rdf+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"rdf\",\n            \"owl\"\n        ]\n    },\n    \"application/reginfo+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"rif\"\n        ]\n    },\n    \"application/relax-ng-compact-syntax\": {\n        source: \"iana\",\n        extensions: [\n            \"rnc\"\n        ]\n    },\n    \"application/resource-lists+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"rl\"\n        ]\n    },\n    \"application/resource-lists-diff+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"rld\"\n        ]\n    },\n    \"application/rls-services+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"rs\"\n        ]\n    },\n    \"application/route-apd+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"rapd\"\n        ]\n    },\n    \"application/route-s-tsid+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"sls\"\n        ]\n    },\n    \"application/route-usd+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"rusd\"\n        ]\n    },\n    \"application/rpki-ghostbusters\": {\n        source: \"iana\",\n        extensions: [\n            \"gbr\"\n        ]\n    },\n    \"application/rpki-manifest\": {\n        source: \"iana\",\n        extensions: [\n            \"mft\"\n        ]\n    },\n    \"application/rpki-roa\": {\n        source: \"iana\",\n        extensions: [\n            \"roa\"\n        ]\n    },\n    \"application/rsd+xml\": {\n        source: \"apache\",\n        extensions: [\n            \"rsd\"\n        ]\n    },\n    \"application/rss+xml\": {\n        source: \"apache\",\n        extensions: [\n            \"rss\"\n        ]\n    },\n    \"application/rtf\": {\n        source: \"iana\",\n        extensions: [\n            \"rtf\"\n        ]\n    },\n    \"application/sbml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"sbml\"\n        ]\n    },\n    \"application/scvp-cv-request\": {\n        source: \"iana\",\n        extensions: [\n            \"scq\"\n        ]\n    },\n    \"application/scvp-cv-response\": {\n        source: \"iana\",\n        extensions: [\n            \"scs\"\n        ]\n    },\n    \"application/scvp-vp-request\": {\n        source: \"iana\",\n        extensions: [\n            \"spq\"\n        ]\n    },\n    \"application/scvp-vp-response\": {\n        source: \"iana\",\n        extensions: [\n            \"spp\"\n        ]\n    },\n    \"application/sdp\": {\n        source: \"iana\",\n        extensions: [\n            \"sdp\"\n        ]\n    },\n    \"application/senml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"senmlx\"\n        ]\n    },\n    \"application/sensml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"sensmlx\"\n        ]\n    },\n    \"application/set-payment-initiation\": {\n        source: \"iana\",\n        extensions: [\n            \"setpay\"\n        ]\n    },\n    \"application/set-registration-initiation\": {\n        source: \"iana\",\n        extensions: [\n            \"setreg\"\n        ]\n    },\n    \"application/shf+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"shf\"\n        ]\n    },\n    \"application/sieve\": {\n        source: \"iana\",\n        extensions: [\n            \"siv\",\n            \"sieve\"\n        ]\n    },\n    \"application/smil+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"smi\",\n            \"smil\"\n        ]\n    },\n    \"application/sparql-query\": {\n        source: \"iana\",\n        extensions: [\n            \"rq\"\n        ]\n    },\n    \"application/sparql-results+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"srx\"\n        ]\n    },\n    \"application/srgs\": {\n        source: \"iana\",\n        extensions: [\n            \"gram\"\n        ]\n    },\n    \"application/srgs+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"grxml\"\n        ]\n    },\n    \"application/sru+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"sru\"\n        ]\n    },\n    \"application/ssdl+xml\": {\n        source: \"apache\",\n        extensions: [\n            \"ssdl\"\n        ]\n    },\n    \"application/ssml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"ssml\"\n        ]\n    },\n    \"application/swid+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"swidtag\"\n        ]\n    },\n    \"application/tei+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"tei\",\n            \"teicorpus\"\n        ]\n    },\n    \"application/thraud+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"tfi\"\n        ]\n    },\n    \"application/timestamped-data\": {\n        source: \"iana\",\n        extensions: [\n            \"tsd\"\n        ]\n    },\n    \"application/trig\": {\n        source: \"iana\",\n        extensions: [\n            \"trig\"\n        ]\n    },\n    \"application/ttml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"ttml\"\n        ]\n    },\n    \"application/urc-ressheet+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"rsheet\"\n        ]\n    },\n    \"application/urc-targetdesc+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"td\"\n        ]\n    },\n    \"application/vnd.1000minds.decision-model+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"1km\"\n        ]\n    },\n    \"application/vnd.3gpp.pic-bw-large\": {\n        source: \"iana\",\n        extensions: [\n            \"plb\"\n        ]\n    },\n    \"application/vnd.3gpp.pic-bw-small\": {\n        source: \"iana\",\n        extensions: [\n            \"psb\"\n        ]\n    },\n    \"application/vnd.3gpp.pic-bw-var\": {\n        source: \"iana\",\n        extensions: [\n            \"pvb\"\n        ]\n    },\n    \"application/vnd.3gpp2.tcap\": {\n        source: \"iana\",\n        extensions: [\n            \"tcap\"\n        ]\n    },\n    \"application/vnd.3m.post-it-notes\": {\n        source: \"iana\",\n        extensions: [\n            \"pwn\"\n        ]\n    },\n    \"application/vnd.accpac.simply.aso\": {\n        source: \"iana\",\n        extensions: [\n            \"aso\"\n        ]\n    },\n    \"application/vnd.accpac.simply.imp\": {\n        source: \"iana\",\n        extensions: [\n            \"imp\"\n        ]\n    },\n    \"application/vnd.acucobol\": {\n        source: \"iana\",\n        extensions: [\n            \"acu\"\n        ]\n    },\n    \"application/vnd.acucorp\": {\n        source: \"iana\",\n        extensions: [\n            \"atc\",\n            \"acutc\"\n        ]\n    },\n    \"application/vnd.adobe.air-application-installer-package+zip\": {\n        source: \"apache\",\n        extensions: [\n            \"air\"\n        ]\n    },\n    \"application/vnd.adobe.formscentral.fcdt\": {\n        source: \"iana\",\n        extensions: [\n            \"fcdt\"\n        ]\n    },\n    \"application/vnd.adobe.fxp\": {\n        source: \"iana\",\n        extensions: [\n            \"fxp\",\n            \"fxpl\"\n        ]\n    },\n    \"application/vnd.adobe.xdp+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xdp\"\n        ]\n    },\n    \"application/vnd.adobe.xfdf\": {\n        source: \"iana\",\n        extensions: [\n            \"xfdf\"\n        ]\n    },\n    \"application/vnd.age\": {\n        source: \"iana\",\n        extensions: [\n            \"age\"\n        ]\n    },\n    \"application/vnd.ahead.space\": {\n        source: \"iana\",\n        extensions: [\n            \"ahead\"\n        ]\n    },\n    \"application/vnd.airzip.filesecure.azf\": {\n        source: \"iana\",\n        extensions: [\n            \"azf\"\n        ]\n    },\n    \"application/vnd.airzip.filesecure.azs\": {\n        source: \"iana\",\n        extensions: [\n            \"azs\"\n        ]\n    },\n    \"application/vnd.amazon.ebook\": {\n        source: \"apache\",\n        extensions: [\n            \"azw\"\n        ]\n    },\n    \"application/vnd.americandynamics.acc\": {\n        source: \"iana\",\n        extensions: [\n            \"acc\"\n        ]\n    },\n    \"application/vnd.amiga.ami\": {\n        source: \"iana\",\n        extensions: [\n            \"ami\"\n        ]\n    },\n    \"application/vnd.android.package-archive\": {\n        source: \"apache\",\n        extensions: [\n            \"apk\"\n        ]\n    },\n    \"application/vnd.anser-web-certificate-issue-initiation\": {\n        source: \"iana\",\n        extensions: [\n            \"cii\"\n        ]\n    },\n    \"application/vnd.anser-web-funds-transfer-initiation\": {\n        source: \"apache\",\n        extensions: [\n            \"fti\"\n        ]\n    },\n    \"application/vnd.antix.game-component\": {\n        source: \"iana\",\n        extensions: [\n            \"atx\"\n        ]\n    },\n    \"application/vnd.apple.installer+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"mpkg\"\n        ]\n    },\n    \"application/vnd.apple.keynote\": {\n        source: \"iana\",\n        extensions: [\n            \"key\"\n        ]\n    },\n    \"application/vnd.apple.mpegurl\": {\n        source: \"iana\",\n        extensions: [\n            \"m3u8\"\n        ]\n    },\n    \"application/vnd.apple.numbers\": {\n        source: \"iana\",\n        extensions: [\n            \"numbers\"\n        ]\n    },\n    \"application/vnd.apple.pages\": {\n        source: \"iana\",\n        extensions: [\n            \"pages\"\n        ]\n    },\n    \"application/vnd.aristanetworks.swi\": {\n        source: \"iana\",\n        extensions: [\n            \"swi\"\n        ]\n    },\n    \"application/vnd.astraea-software.iota\": {\n        source: \"iana\",\n        extensions: [\n            \"iota\"\n        ]\n    },\n    \"application/vnd.audiograph\": {\n        source: \"iana\",\n        extensions: [\n            \"aep\"\n        ]\n    },\n    \"application/vnd.balsamiq.bmml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"bmml\"\n        ]\n    },\n    \"application/vnd.blueice.multipass\": {\n        source: \"iana\",\n        extensions: [\n            \"mpm\"\n        ]\n    },\n    \"application/vnd.bmi\": {\n        source: \"iana\",\n        extensions: [\n            \"bmi\"\n        ]\n    },\n    \"application/vnd.businessobjects\": {\n        source: \"iana\",\n        extensions: [\n            \"rep\"\n        ]\n    },\n    \"application/vnd.chemdraw+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"cdxml\"\n        ]\n    },\n    \"application/vnd.chipnuts.karaoke-mmd\": {\n        source: \"iana\",\n        extensions: [\n            \"mmd\"\n        ]\n    },\n    \"application/vnd.cinderella\": {\n        source: \"iana\",\n        extensions: [\n            \"cdy\"\n        ]\n    },\n    \"application/vnd.citationstyles.style+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"csl\"\n        ]\n    },\n    \"application/vnd.claymore\": {\n        source: \"iana\",\n        extensions: [\n            \"cla\"\n        ]\n    },\n    \"application/vnd.cloanto.rp9\": {\n        source: \"iana\",\n        extensions: [\n            \"rp9\"\n        ]\n    },\n    \"application/vnd.clonk.c4group\": {\n        source: \"iana\",\n        extensions: [\n            \"c4g\",\n            \"c4d\",\n            \"c4f\",\n            \"c4p\",\n            \"c4u\"\n        ]\n    },\n    \"application/vnd.cluetrust.cartomobile-config\": {\n        source: \"iana\",\n        extensions: [\n            \"c11amc\"\n        ]\n    },\n    \"application/vnd.cluetrust.cartomobile-config-pkg\": {\n        source: \"iana\",\n        extensions: [\n            \"c11amz\"\n        ]\n    },\n    \"application/vnd.commonspace\": {\n        source: \"iana\",\n        extensions: [\n            \"csp\"\n        ]\n    },\n    \"application/vnd.contact.cmsg\": {\n        source: \"iana\",\n        extensions: [\n            \"cdbcmsg\"\n        ]\n    },\n    \"application/vnd.cosmocaller\": {\n        source: \"iana\",\n        extensions: [\n            \"cmc\"\n        ]\n    },\n    \"application/vnd.crick.clicker\": {\n        source: \"iana\",\n        extensions: [\n            \"clkx\"\n        ]\n    },\n    \"application/vnd.crick.clicker.keyboard\": {\n        source: \"iana\",\n        extensions: [\n            \"clkk\"\n        ]\n    },\n    \"application/vnd.crick.clicker.palette\": {\n        source: \"iana\",\n        extensions: [\n            \"clkp\"\n        ]\n    },\n    \"application/vnd.crick.clicker.template\": {\n        source: \"iana\",\n        extensions: [\n            \"clkt\"\n        ]\n    },\n    \"application/vnd.crick.clicker.wordbank\": {\n        source: \"iana\",\n        extensions: [\n            \"clkw\"\n        ]\n    },\n    \"application/vnd.criticaltools.wbs+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"wbs\"\n        ]\n    },\n    \"application/vnd.ctc-posml\": {\n        source: \"iana\",\n        extensions: [\n            \"pml\"\n        ]\n    },\n    \"application/vnd.cups-ppd\": {\n        source: \"iana\",\n        extensions: [\n            \"ppd\"\n        ]\n    },\n    \"application/vnd.curl.car\": {\n        source: \"apache\",\n        extensions: [\n            \"car\"\n        ]\n    },\n    \"application/vnd.curl.pcurl\": {\n        source: \"apache\",\n        extensions: [\n            \"pcurl\"\n        ]\n    },\n    \"application/vnd.dart\": {\n        source: \"iana\",\n        extensions: [\n            \"dart\"\n        ]\n    },\n    \"application/vnd.data-vision.rdz\": {\n        source: \"iana\",\n        extensions: [\n            \"rdz\"\n        ]\n    },\n    \"application/vnd.dbf\": {\n        source: \"iana\",\n        extensions: [\n            \"dbf\"\n        ]\n    },\n    \"application/vnd.dece.data\": {\n        source: \"iana\",\n        extensions: [\n            \"uvf\",\n            \"uvvf\",\n            \"uvd\",\n            \"uvvd\"\n        ]\n    },\n    \"application/vnd.dece.ttml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"uvt\",\n            \"uvvt\"\n        ]\n    },\n    \"application/vnd.dece.unspecified\": {\n        source: \"iana\",\n        extensions: [\n            \"uvx\",\n            \"uvvx\"\n        ]\n    },\n    \"application/vnd.dece.zip\": {\n        source: \"iana\",\n        extensions: [\n            \"uvz\",\n            \"uvvz\"\n        ]\n    },\n    \"application/vnd.denovo.fcselayout-link\": {\n        source: \"iana\",\n        extensions: [\n            \"fe_launch\"\n        ]\n    },\n    \"application/vnd.dna\": {\n        source: \"iana\",\n        extensions: [\n            \"dna\"\n        ]\n    },\n    \"application/vnd.dolby.mlp\": {\n        source: \"apache\",\n        extensions: [\n            \"mlp\"\n        ]\n    },\n    \"application/vnd.dpgraph\": {\n        source: \"iana\",\n        extensions: [\n            \"dpg\"\n        ]\n    },\n    \"application/vnd.dreamfactory\": {\n        source: \"iana\",\n        extensions: [\n            \"dfac\"\n        ]\n    },\n    \"application/vnd.ds-keypoint\": {\n        source: \"apache\",\n        extensions: [\n            \"kpxx\"\n        ]\n    },\n    \"application/vnd.dvb.ait\": {\n        source: \"iana\",\n        extensions: [\n            \"ait\"\n        ]\n    },\n    \"application/vnd.dvb.service\": {\n        source: \"iana\",\n        extensions: [\n            \"svc\"\n        ]\n    },\n    \"application/vnd.dynageo\": {\n        source: \"iana\",\n        extensions: [\n            \"geo\"\n        ]\n    },\n    \"application/vnd.ecowin.chart\": {\n        source: \"iana\",\n        extensions: [\n            \"mag\"\n        ]\n    },\n    \"application/vnd.enliven\": {\n        source: \"iana\",\n        extensions: [\n            \"nml\"\n        ]\n    },\n    \"application/vnd.epson.esf\": {\n        source: \"iana\",\n        extensions: [\n            \"esf\"\n        ]\n    },\n    \"application/vnd.epson.msf\": {\n        source: \"iana\",\n        extensions: [\n            \"msf\"\n        ]\n    },\n    \"application/vnd.epson.quickanime\": {\n        source: \"iana\",\n        extensions: [\n            \"qam\"\n        ]\n    },\n    \"application/vnd.epson.salt\": {\n        source: \"iana\",\n        extensions: [\n            \"slt\"\n        ]\n    },\n    \"application/vnd.epson.ssf\": {\n        source: \"iana\",\n        extensions: [\n            \"ssf\"\n        ]\n    },\n    \"application/vnd.eszigno3+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"es3\",\n            \"et3\"\n        ]\n    },\n    \"application/vnd.ezpix-album\": {\n        source: \"iana\",\n        extensions: [\n            \"ez2\"\n        ]\n    },\n    \"application/vnd.ezpix-package\": {\n        source: \"iana\",\n        extensions: [\n            \"ez3\"\n        ]\n    },\n    \"application/vnd.fdf\": {\n        source: \"iana\",\n        extensions: [\n            \"fdf\"\n        ]\n    },\n    \"application/vnd.fdsn.mseed\": {\n        source: \"iana\",\n        extensions: [\n            \"mseed\"\n        ]\n    },\n    \"application/vnd.fdsn.seed\": {\n        source: \"iana\",\n        extensions: [\n            \"seed\",\n            \"dataless\"\n        ]\n    },\n    \"application/vnd.flographit\": {\n        source: \"iana\",\n        extensions: [\n            \"gph\"\n        ]\n    },\n    \"application/vnd.fluxtime.clip\": {\n        source: \"iana\",\n        extensions: [\n            \"ftc\"\n        ]\n    },\n    \"application/vnd.framemaker\": {\n        source: \"iana\",\n        extensions: [\n            \"fm\",\n            \"frame\",\n            \"maker\",\n            \"book\"\n        ]\n    },\n    \"application/vnd.frogans.fnc\": {\n        source: \"iana\",\n        extensions: [\n            \"fnc\"\n        ]\n    },\n    \"application/vnd.frogans.ltf\": {\n        source: \"iana\",\n        extensions: [\n            \"ltf\"\n        ]\n    },\n    \"application/vnd.fsc.weblaunch\": {\n        source: \"iana\",\n        extensions: [\n            \"fsc\"\n        ]\n    },\n    \"application/vnd.fujitsu.oasys\": {\n        source: \"iana\",\n        extensions: [\n            \"oas\"\n        ]\n    },\n    \"application/vnd.fujitsu.oasys2\": {\n        source: \"iana\",\n        extensions: [\n            \"oa2\"\n        ]\n    },\n    \"application/vnd.fujitsu.oasys3\": {\n        source: \"iana\",\n        extensions: [\n            \"oa3\"\n        ]\n    },\n    \"application/vnd.fujitsu.oasysgp\": {\n        source: \"iana\",\n        extensions: [\n            \"fg5\"\n        ]\n    },\n    \"application/vnd.fujitsu.oasysprs\": {\n        source: \"iana\",\n        extensions: [\n            \"bh2\"\n        ]\n    },\n    \"application/vnd.fujixerox.ddd\": {\n        source: \"iana\",\n        extensions: [\n            \"ddd\"\n        ]\n    },\n    \"application/vnd.fujixerox.docuworks\": {\n        source: \"iana\",\n        extensions: [\n            \"xdw\"\n        ]\n    },\n    \"application/vnd.fujixerox.docuworks.binder\": {\n        source: \"iana\",\n        extensions: [\n            \"xbd\"\n        ]\n    },\n    \"application/vnd.fuzzysheet\": {\n        source: \"iana\",\n        extensions: [\n            \"fzs\"\n        ]\n    },\n    \"application/vnd.genomatix.tuxedo\": {\n        source: \"iana\",\n        extensions: [\n            \"txd\"\n        ]\n    },\n    \"application/vnd.geogebra.file\": {\n        source: \"iana\",\n        extensions: [\n            \"ggb\"\n        ]\n    },\n    \"application/vnd.geogebra.tool\": {\n        source: \"iana\",\n        extensions: [\n            \"ggt\"\n        ]\n    },\n    \"application/vnd.geometry-explorer\": {\n        source: \"iana\",\n        extensions: [\n            \"gex\",\n            \"gre\"\n        ]\n    },\n    \"application/vnd.geonext\": {\n        source: \"iana\",\n        extensions: [\n            \"gxt\"\n        ]\n    },\n    \"application/vnd.geoplan\": {\n        source: \"iana\",\n        extensions: [\n            \"g2w\"\n        ]\n    },\n    \"application/vnd.geospace\": {\n        source: \"iana\",\n        extensions: [\n            \"g3w\"\n        ]\n    },\n    \"application/vnd.gmx\": {\n        source: \"iana\",\n        extensions: [\n            \"gmx\"\n        ]\n    },\n    \"application/vnd.google-earth.kml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"kml\"\n        ]\n    },\n    \"application/vnd.google-earth.kmz\": {\n        source: \"iana\",\n        extensions: [\n            \"kmz\"\n        ]\n    },\n    \"application/vnd.grafeq\": {\n        source: \"iana\",\n        extensions: [\n            \"gqf\",\n            \"gqs\"\n        ]\n    },\n    \"application/vnd.groove-account\": {\n        source: \"iana\",\n        extensions: [\n            \"gac\"\n        ]\n    },\n    \"application/vnd.groove-help\": {\n        source: \"iana\",\n        extensions: [\n            \"ghf\"\n        ]\n    },\n    \"application/vnd.groove-identity-message\": {\n        source: \"iana\",\n        extensions: [\n            \"gim\"\n        ]\n    },\n    \"application/vnd.groove-injector\": {\n        source: \"iana\",\n        extensions: [\n            \"grv\"\n        ]\n    },\n    \"application/vnd.groove-tool-message\": {\n        source: \"iana\",\n        extensions: [\n            \"gtm\"\n        ]\n    },\n    \"application/vnd.groove-tool-template\": {\n        source: \"iana\",\n        extensions: [\n            \"tpl\"\n        ]\n    },\n    \"application/vnd.groove-vcard\": {\n        source: \"iana\",\n        extensions: [\n            \"vcg\"\n        ]\n    },\n    \"application/vnd.hal+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"hal\"\n        ]\n    },\n    \"application/vnd.handheld-entertainment+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"zmm\"\n        ]\n    },\n    \"application/vnd.hbci\": {\n        source: \"iana\",\n        extensions: [\n            \"hbci\"\n        ]\n    },\n    \"application/vnd.hhe.lesson-player\": {\n        source: \"iana\",\n        extensions: [\n            \"les\"\n        ]\n    },\n    \"application/vnd.hp-hpgl\": {\n        source: \"iana\",\n        extensions: [\n            \"hpgl\"\n        ]\n    },\n    \"application/vnd.hp-hpid\": {\n        source: \"iana\",\n        extensions: [\n            \"hpid\"\n        ]\n    },\n    \"application/vnd.hp-hps\": {\n        source: \"iana\",\n        extensions: [\n            \"hps\"\n        ]\n    },\n    \"application/vnd.hp-jlyt\": {\n        source: \"iana\",\n        extensions: [\n            \"jlt\"\n        ]\n    },\n    \"application/vnd.hp-pcl\": {\n        source: \"iana\",\n        extensions: [\n            \"pcl\"\n        ]\n    },\n    \"application/vnd.hp-pclxl\": {\n        source: \"iana\",\n        extensions: [\n            \"pclxl\"\n        ]\n    },\n    \"application/vnd.hydrostatix.sof-data\": {\n        source: \"iana\",\n        extensions: [\n            \"sfd-hdstx\"\n        ]\n    },\n    \"application/vnd.ibm.minipay\": {\n        source: \"iana\",\n        extensions: [\n            \"mpy\"\n        ]\n    },\n    \"application/vnd.ibm.modcap\": {\n        source: \"iana\",\n        extensions: [\n            \"afp\",\n            \"listafp\",\n            \"list3820\"\n        ]\n    },\n    \"application/vnd.ibm.rights-management\": {\n        source: \"iana\",\n        extensions: [\n            \"irm\"\n        ]\n    },\n    \"application/vnd.ibm.secure-container\": {\n        source: \"iana\",\n        extensions: [\n            \"sc\"\n        ]\n    },\n    \"application/vnd.iccprofile\": {\n        source: \"iana\",\n        extensions: [\n            \"icc\",\n            \"icm\"\n        ]\n    },\n    \"application/vnd.igloader\": {\n        source: \"iana\",\n        extensions: [\n            \"igl\"\n        ]\n    },\n    \"application/vnd.immervision-ivp\": {\n        source: \"iana\",\n        extensions: [\n            \"ivp\"\n        ]\n    },\n    \"application/vnd.immervision-ivu\": {\n        source: \"iana\",\n        extensions: [\n            \"ivu\"\n        ]\n    },\n    \"application/vnd.insors.igm\": {\n        source: \"iana\",\n        extensions: [\n            \"igm\"\n        ]\n    },\n    \"application/vnd.intercon.formnet\": {\n        source: \"iana\",\n        extensions: [\n            \"xpw\",\n            \"xpx\"\n        ]\n    },\n    \"application/vnd.intergeo\": {\n        source: \"iana\",\n        extensions: [\n            \"i2g\"\n        ]\n    },\n    \"application/vnd.intu.qbo\": {\n        source: \"iana\",\n        extensions: [\n            \"qbo\"\n        ]\n    },\n    \"application/vnd.intu.qfx\": {\n        source: \"iana\",\n        extensions: [\n            \"qfx\"\n        ]\n    },\n    \"application/vnd.ipunplugged.rcprofile\": {\n        source: \"iana\",\n        extensions: [\n            \"rcprofile\"\n        ]\n    },\n    \"application/vnd.irepository.package+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"irp\"\n        ]\n    },\n    \"application/vnd.is-xpr\": {\n        source: \"iana\",\n        extensions: [\n            \"xpr\"\n        ]\n    },\n    \"application/vnd.isac.fcs\": {\n        source: \"iana\",\n        extensions: [\n            \"fcs\"\n        ]\n    },\n    \"application/vnd.jam\": {\n        source: \"iana\",\n        extensions: [\n            \"jam\"\n        ]\n    },\n    \"application/vnd.jcp.javame.midlet-rms\": {\n        source: \"iana\",\n        extensions: [\n            \"rms\"\n        ]\n    },\n    \"application/vnd.jisp\": {\n        source: \"iana\",\n        extensions: [\n            \"jisp\"\n        ]\n    },\n    \"application/vnd.joost.joda-archive\": {\n        source: \"iana\",\n        extensions: [\n            \"joda\"\n        ]\n    },\n    \"application/vnd.kahootz\": {\n        source: \"iana\",\n        extensions: [\n            \"ktz\",\n            \"ktr\"\n        ]\n    },\n    \"application/vnd.kde.karbon\": {\n        source: \"iana\",\n        extensions: [\n            \"karbon\"\n        ]\n    },\n    \"application/vnd.kde.kchart\": {\n        source: \"iana\",\n        extensions: [\n            \"chrt\"\n        ]\n    },\n    \"application/vnd.kde.kformula\": {\n        source: \"iana\",\n        extensions: [\n            \"kfo\"\n        ]\n    },\n    \"application/vnd.kde.kivio\": {\n        source: \"iana\",\n        extensions: [\n            \"flw\"\n        ]\n    },\n    \"application/vnd.kde.kontour\": {\n        source: \"iana\",\n        extensions: [\n            \"kon\"\n        ]\n    },\n    \"application/vnd.kde.kpresenter\": {\n        source: \"iana\",\n        extensions: [\n            \"kpr\",\n            \"kpt\"\n        ]\n    },\n    \"application/vnd.kde.kspread\": {\n        source: \"iana\",\n        extensions: [\n            \"ksp\"\n        ]\n    },\n    \"application/vnd.kde.kword\": {\n        source: \"iana\",\n        extensions: [\n            \"kwd\",\n            \"kwt\"\n        ]\n    },\n    \"application/vnd.kenameaapp\": {\n        source: \"iana\",\n        extensions: [\n            \"htke\"\n        ]\n    },\n    \"application/vnd.kidspiration\": {\n        source: \"iana\",\n        extensions: [\n            \"kia\"\n        ]\n    },\n    \"application/vnd.kinar\": {\n        source: \"iana\",\n        extensions: [\n            \"kne\",\n            \"knp\"\n        ]\n    },\n    \"application/vnd.koan\": {\n        source: \"iana\",\n        extensions: [\n            \"skp\",\n            \"skd\",\n            \"skt\",\n            \"skm\"\n        ]\n    },\n    \"application/vnd.kodak-descriptor\": {\n        source: \"iana\",\n        extensions: [\n            \"sse\"\n        ]\n    },\n    \"application/vnd.las.las+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"lasxml\"\n        ]\n    },\n    \"application/vnd.llamagraphics.life-balance.desktop\": {\n        source: \"iana\",\n        extensions: [\n            \"lbd\"\n        ]\n    },\n    \"application/vnd.llamagraphics.life-balance.exchange+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"lbe\"\n        ]\n    },\n    \"application/vnd.lotus-1-2-3\": {\n        source: \"iana\",\n        extensions: [\n            \"123\"\n        ]\n    },\n    \"application/vnd.lotus-approach\": {\n        source: \"iana\",\n        extensions: [\n            \"apr\"\n        ]\n    },\n    \"application/vnd.lotus-freelance\": {\n        source: \"iana\",\n        extensions: [\n            \"pre\"\n        ]\n    },\n    \"application/vnd.lotus-notes\": {\n        source: \"iana\",\n        extensions: [\n            \"nsf\"\n        ]\n    },\n    \"application/vnd.lotus-organizer\": {\n        source: \"iana\",\n        extensions: [\n            \"org\"\n        ]\n    },\n    \"application/vnd.lotus-screencam\": {\n        source: \"iana\",\n        extensions: [\n            \"scm\"\n        ]\n    },\n    \"application/vnd.lotus-wordpro\": {\n        source: \"iana\",\n        extensions: [\n            \"lwp\"\n        ]\n    },\n    \"application/vnd.macports.portpkg\": {\n        source: \"iana\",\n        extensions: [\n            \"portpkg\"\n        ]\n    },\n    \"application/vnd.mapbox-vector-tile\": {\n        source: \"iana\",\n        extensions: [\n            \"mvt\"\n        ]\n    },\n    \"application/vnd.mcd\": {\n        source: \"iana\",\n        extensions: [\n            \"mcd\"\n        ]\n    },\n    \"application/vnd.medcalcdata\": {\n        source: \"iana\",\n        extensions: [\n            \"mc1\"\n        ]\n    },\n    \"application/vnd.mediastation.cdkey\": {\n        source: \"iana\",\n        extensions: [\n            \"cdkey\"\n        ]\n    },\n    \"application/vnd.mfer\": {\n        source: \"iana\",\n        extensions: [\n            \"mwf\"\n        ]\n    },\n    \"application/vnd.mfmp\": {\n        source: \"iana\",\n        extensions: [\n            \"mfm\"\n        ]\n    },\n    \"application/vnd.micrografx.flo\": {\n        source: \"iana\",\n        extensions: [\n            \"flo\"\n        ]\n    },\n    \"application/vnd.micrografx.igx\": {\n        source: \"iana\",\n        extensions: [\n            \"igx\"\n        ]\n    },\n    \"application/vnd.mif\": {\n        source: \"iana\",\n        extensions: [\n            \"mif\"\n        ]\n    },\n    \"application/vnd.mobius.daf\": {\n        source: \"iana\",\n        extensions: [\n            \"daf\"\n        ]\n    },\n    \"application/vnd.mobius.dis\": {\n        source: \"iana\",\n        extensions: [\n            \"dis\"\n        ]\n    },\n    \"application/vnd.mobius.mbk\": {\n        source: \"iana\",\n        extensions: [\n            \"mbk\"\n        ]\n    },\n    \"application/vnd.mobius.mqy\": {\n        source: \"iana\",\n        extensions: [\n            \"mqy\"\n        ]\n    },\n    \"application/vnd.mobius.msl\": {\n        source: \"iana\",\n        extensions: [\n            \"msl\"\n        ]\n    },\n    \"application/vnd.mobius.plc\": {\n        source: \"iana\",\n        extensions: [\n            \"plc\"\n        ]\n    },\n    \"application/vnd.mobius.txf\": {\n        source: \"iana\",\n        extensions: [\n            \"txf\"\n        ]\n    },\n    \"application/vnd.mophun.application\": {\n        source: \"iana\",\n        extensions: [\n            \"mpn\"\n        ]\n    },\n    \"application/vnd.mophun.certificate\": {\n        source: \"iana\",\n        extensions: [\n            \"mpc\"\n        ]\n    },\n    \"application/vnd.mozilla.xul+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xul\"\n        ]\n    },\n    \"application/vnd.ms-artgalry\": {\n        source: \"iana\",\n        extensions: [\n            \"cil\"\n        ]\n    },\n    \"application/vnd.ms-cab-compressed\": {\n        source: \"iana\",\n        extensions: [\n            \"cab\"\n        ]\n    },\n    \"application/vnd.ms-excel\": {\n        source: \"iana\",\n        extensions: [\n            \"xls\",\n            \"xlm\",\n            \"xla\",\n            \"xlc\",\n            \"xlt\",\n            \"xlw\"\n        ]\n    },\n    \"application/vnd.ms-excel.addin.macroenabled.12\": {\n        source: \"iana\",\n        extensions: [\n            \"xlam\"\n        ]\n    },\n    \"application/vnd.ms-excel.sheet.binary.macroenabled.12\": {\n        source: \"iana\",\n        extensions: [\n            \"xlsb\"\n        ]\n    },\n    \"application/vnd.ms-excel.sheet.macroenabled.12\": {\n        source: \"iana\",\n        extensions: [\n            \"xlsm\"\n        ]\n    },\n    \"application/vnd.ms-excel.template.macroenabled.12\": {\n        source: \"iana\",\n        extensions: [\n            \"xltm\"\n        ]\n    },\n    \"application/vnd.ms-fontobject\": {\n        source: \"iana\",\n        extensions: [\n            \"eot\"\n        ]\n    },\n    \"application/vnd.ms-htmlhelp\": {\n        source: \"iana\",\n        extensions: [\n            \"chm\"\n        ]\n    },\n    \"application/vnd.ms-ims\": {\n        source: \"iana\",\n        extensions: [\n            \"ims\"\n        ]\n    },\n    \"application/vnd.ms-lrm\": {\n        source: \"iana\",\n        extensions: [\n            \"lrm\"\n        ]\n    },\n    \"application/vnd.ms-officetheme\": {\n        source: \"iana\",\n        extensions: [\n            \"thmx\"\n        ]\n    },\n    \"application/vnd.ms-pki.seccat\": {\n        source: \"apache\",\n        extensions: [\n            \"cat\"\n        ]\n    },\n    \"application/vnd.ms-pki.stl\": {\n        source: \"apache\",\n        extensions: [\n            \"stl\"\n        ]\n    },\n    \"application/vnd.ms-powerpoint\": {\n        source: \"iana\",\n        extensions: [\n            \"ppt\",\n            \"pps\",\n            \"pot\"\n        ]\n    },\n    \"application/vnd.ms-powerpoint.addin.macroenabled.12\": {\n        source: \"iana\",\n        extensions: [\n            \"ppam\"\n        ]\n    },\n    \"application/vnd.ms-powerpoint.presentation.macroenabled.12\": {\n        source: \"iana\",\n        extensions: [\n            \"pptm\"\n        ]\n    },\n    \"application/vnd.ms-powerpoint.slide.macroenabled.12\": {\n        source: \"iana\",\n        extensions: [\n            \"sldm\"\n        ]\n    },\n    \"application/vnd.ms-powerpoint.slideshow.macroenabled.12\": {\n        source: \"iana\",\n        extensions: [\n            \"ppsm\"\n        ]\n    },\n    \"application/vnd.ms-powerpoint.template.macroenabled.12\": {\n        source: \"iana\",\n        extensions: [\n            \"potm\"\n        ]\n    },\n    \"application/vnd.ms-project\": {\n        source: \"iana\",\n        extensions: [\n            \"mpp\",\n            \"mpt\"\n        ]\n    },\n    \"application/vnd.ms-word.document.macroenabled.12\": {\n        source: \"iana\",\n        extensions: [\n            \"docm\"\n        ]\n    },\n    \"application/vnd.ms-word.template.macroenabled.12\": {\n        source: \"iana\",\n        extensions: [\n            \"dotm\"\n        ]\n    },\n    \"application/vnd.ms-works\": {\n        source: \"iana\",\n        extensions: [\n            \"wps\",\n            \"wks\",\n            \"wcm\",\n            \"wdb\"\n        ]\n    },\n    \"application/vnd.ms-wpl\": {\n        source: \"iana\",\n        extensions: [\n            \"wpl\"\n        ]\n    },\n    \"application/vnd.ms-xpsdocument\": {\n        source: \"iana\",\n        extensions: [\n            \"xps\"\n        ]\n    },\n    \"application/vnd.mseq\": {\n        source: \"iana\",\n        extensions: [\n            \"mseq\"\n        ]\n    },\n    \"application/vnd.musician\": {\n        source: \"iana\",\n        extensions: [\n            \"mus\"\n        ]\n    },\n    \"application/vnd.muvee.style\": {\n        source: \"iana\",\n        extensions: [\n            \"msty\"\n        ]\n    },\n    \"application/vnd.mynfc\": {\n        source: \"iana\",\n        extensions: [\n            \"taglet\"\n        ]\n    },\n    \"application/vnd.neurolanguage.nlu\": {\n        source: \"iana\",\n        extensions: [\n            \"nlu\"\n        ]\n    },\n    \"application/vnd.nitf\": {\n        source: \"iana\",\n        extensions: [\n            \"ntf\",\n            \"nitf\"\n        ]\n    },\n    \"application/vnd.noblenet-directory\": {\n        source: \"iana\",\n        extensions: [\n            \"nnd\"\n        ]\n    },\n    \"application/vnd.noblenet-sealer\": {\n        source: \"iana\",\n        extensions: [\n            \"nns\"\n        ]\n    },\n    \"application/vnd.noblenet-web\": {\n        source: \"iana\",\n        extensions: [\n            \"nnw\"\n        ]\n    },\n    \"application/vnd.nokia.n-gage.ac+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"ac\"\n        ]\n    },\n    \"application/vnd.nokia.n-gage.data\": {\n        source: \"iana\",\n        extensions: [\n            \"ngdat\"\n        ]\n    },\n    \"application/vnd.nokia.n-gage.symbian.install\": {\n        source: \"iana\",\n        extensions: [\n            \"n-gage\"\n        ]\n    },\n    \"application/vnd.nokia.radio-preset\": {\n        source: \"iana\",\n        extensions: [\n            \"rpst\"\n        ]\n    },\n    \"application/vnd.nokia.radio-presets\": {\n        source: \"iana\",\n        extensions: [\n            \"rpss\"\n        ]\n    },\n    \"application/vnd.novadigm.edm\": {\n        source: \"iana\",\n        extensions: [\n            \"edm\"\n        ]\n    },\n    \"application/vnd.novadigm.edx\": {\n        source: \"iana\",\n        extensions: [\n            \"edx\"\n        ]\n    },\n    \"application/vnd.novadigm.ext\": {\n        source: \"iana\",\n        extensions: [\n            \"ext\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.chart\": {\n        source: \"iana\",\n        extensions: [\n            \"odc\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.chart-template\": {\n        source: \"iana\",\n        extensions: [\n            \"otc\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.database\": {\n        source: \"iana\",\n        extensions: [\n            \"odb\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.formula\": {\n        source: \"iana\",\n        extensions: [\n            \"odf\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.formula-template\": {\n        source: \"iana\",\n        extensions: [\n            \"odft\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.graphics\": {\n        source: \"iana\",\n        extensions: [\n            \"odg\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.graphics-template\": {\n        source: \"iana\",\n        extensions: [\n            \"otg\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.image\": {\n        source: \"iana\",\n        extensions: [\n            \"odi\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.image-template\": {\n        source: \"iana\",\n        extensions: [\n            \"oti\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.presentation\": {\n        source: \"iana\",\n        extensions: [\n            \"odp\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.presentation-template\": {\n        source: \"iana\",\n        extensions: [\n            \"otp\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.spreadsheet\": {\n        source: \"iana\",\n        extensions: [\n            \"ods\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.spreadsheet-template\": {\n        source: \"iana\",\n        extensions: [\n            \"ots\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.text\": {\n        source: \"iana\",\n        extensions: [\n            \"odt\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.text-master\": {\n        source: \"iana\",\n        extensions: [\n            \"odm\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.text-template\": {\n        source: \"iana\",\n        extensions: [\n            \"ott\"\n        ]\n    },\n    \"application/vnd.oasis.opendocument.text-web\": {\n        source: \"iana\",\n        extensions: [\n            \"oth\"\n        ]\n    },\n    \"application/vnd.olpc-sugar\": {\n        source: \"iana\",\n        extensions: [\n            \"xo\"\n        ]\n    },\n    \"application/vnd.oma.dd2+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"dd2\"\n        ]\n    },\n    \"application/vnd.openblox.game+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"obgx\"\n        ]\n    },\n    \"application/vnd.openofficeorg.extension\": {\n        source: \"apache\",\n        extensions: [\n            \"oxt\"\n        ]\n    },\n    \"application/vnd.openstreetmap.data+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"osm\"\n        ]\n    },\n    \"application/vnd.openxmlformats-officedocument.presentationml.presentation\": {\n        source: \"iana\",\n        extensions: [\n            \"pptx\"\n        ]\n    },\n    \"application/vnd.openxmlformats-officedocument.presentationml.slide\": {\n        source: \"iana\",\n        extensions: [\n            \"sldx\"\n        ]\n    },\n    \"application/vnd.openxmlformats-officedocument.presentationml.slideshow\": {\n        source: \"iana\",\n        extensions: [\n            \"ppsx\"\n        ]\n    },\n    \"application/vnd.openxmlformats-officedocument.presentationml.template\": {\n        source: \"iana\",\n        extensions: [\n            \"potx\"\n        ]\n    },\n    \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\": {\n        source: \"iana\",\n        extensions: [\n            \"xlsx\"\n        ]\n    },\n    \"application/vnd.openxmlformats-officedocument.spreadsheetml.template\": {\n        source: \"iana\",\n        extensions: [\n            \"xltx\"\n        ]\n    },\n    \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": {\n        source: \"iana\",\n        extensions: [\n            \"docx\"\n        ]\n    },\n    \"application/vnd.openxmlformats-officedocument.wordprocessingml.template\": {\n        source: \"iana\",\n        extensions: [\n            \"dotx\"\n        ]\n    },\n    \"application/vnd.osgeo.mapguide.package\": {\n        source: \"iana\",\n        extensions: [\n            \"mgp\"\n        ]\n    },\n    \"application/vnd.osgi.dp\": {\n        source: \"iana\",\n        extensions: [\n            \"dp\"\n        ]\n    },\n    \"application/vnd.osgi.subsystem\": {\n        source: \"iana\",\n        extensions: [\n            \"esa\"\n        ]\n    },\n    \"application/vnd.palm\": {\n        source: \"iana\",\n        extensions: [\n            \"pdb\",\n            \"pqa\",\n            \"oprc\"\n        ]\n    },\n    \"application/vnd.pawaafile\": {\n        source: \"iana\",\n        extensions: [\n            \"paw\"\n        ]\n    },\n    \"application/vnd.pg.format\": {\n        source: \"iana\",\n        extensions: [\n            \"str\"\n        ]\n    },\n    \"application/vnd.pg.osasli\": {\n        source: \"iana\",\n        extensions: [\n            \"ei6\"\n        ]\n    },\n    \"application/vnd.picsel\": {\n        source: \"iana\",\n        extensions: [\n            \"efif\"\n        ]\n    },\n    \"application/vnd.pmi.widget\": {\n        source: \"iana\",\n        extensions: [\n            \"wg\"\n        ]\n    },\n    \"application/vnd.pocketlearn\": {\n        source: \"iana\",\n        extensions: [\n            \"plf\"\n        ]\n    },\n    \"application/vnd.powerbuilder6\": {\n        source: \"iana\",\n        extensions: [\n            \"pbd\"\n        ]\n    },\n    \"application/vnd.previewsystems.box\": {\n        source: \"iana\",\n        extensions: [\n            \"box\"\n        ]\n    },\n    \"application/vnd.proteus.magazine\": {\n        source: \"iana\",\n        extensions: [\n            \"mgz\"\n        ]\n    },\n    \"application/vnd.publishare-delta-tree\": {\n        source: \"iana\",\n        extensions: [\n            \"qps\"\n        ]\n    },\n    \"application/vnd.pvi.ptid1\": {\n        source: \"iana\",\n        extensions: [\n            \"ptid\"\n        ]\n    },\n    \"application/vnd.quark.quarkxpress\": {\n        source: \"iana\",\n        extensions: [\n            \"qxd\",\n            \"qxt\",\n            \"qwd\",\n            \"qwt\",\n            \"qxl\",\n            \"qxb\"\n        ]\n    },\n    \"application/vnd.rar\": {\n        source: \"iana\",\n        extensions: [\n            \"rar\"\n        ]\n    },\n    \"application/vnd.realvnc.bed\": {\n        source: \"iana\",\n        extensions: [\n            \"bed\"\n        ]\n    },\n    \"application/vnd.recordare.musicxml\": {\n        source: \"iana\",\n        extensions: [\n            \"mxl\"\n        ]\n    },\n    \"application/vnd.recordare.musicxml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"musicxml\"\n        ]\n    },\n    \"application/vnd.rig.cryptonote\": {\n        source: \"iana\",\n        extensions: [\n            \"cryptonote\"\n        ]\n    },\n    \"application/vnd.rim.cod\": {\n        source: \"apache\",\n        extensions: [\n            \"cod\"\n        ]\n    },\n    \"application/vnd.rn-realmedia\": {\n        source: \"apache\",\n        extensions: [\n            \"rm\"\n        ]\n    },\n    \"application/vnd.rn-realmedia-vbr\": {\n        source: \"apache\",\n        extensions: [\n            \"rmvb\"\n        ]\n    },\n    \"application/vnd.route66.link66+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"link66\"\n        ]\n    },\n    \"application/vnd.sailingtracker.track\": {\n        source: \"iana\",\n        extensions: [\n            \"st\"\n        ]\n    },\n    \"application/vnd.seemail\": {\n        source: \"iana\",\n        extensions: [\n            \"see\"\n        ]\n    },\n    \"application/vnd.sema\": {\n        source: \"iana\",\n        extensions: [\n            \"sema\"\n        ]\n    },\n    \"application/vnd.semd\": {\n        source: \"iana\",\n        extensions: [\n            \"semd\"\n        ]\n    },\n    \"application/vnd.semf\": {\n        source: \"iana\",\n        extensions: [\n            \"semf\"\n        ]\n    },\n    \"application/vnd.shana.informed.formdata\": {\n        source: \"iana\",\n        extensions: [\n            \"ifm\"\n        ]\n    },\n    \"application/vnd.shana.informed.formtemplate\": {\n        source: \"iana\",\n        extensions: [\n            \"itp\"\n        ]\n    },\n    \"application/vnd.shana.informed.interchange\": {\n        source: \"iana\",\n        extensions: [\n            \"iif\"\n        ]\n    },\n    \"application/vnd.shana.informed.package\": {\n        source: \"iana\",\n        extensions: [\n            \"ipk\"\n        ]\n    },\n    \"application/vnd.simtech-mindmapper\": {\n        source: \"iana\",\n        extensions: [\n            \"twd\",\n            \"twds\"\n        ]\n    },\n    \"application/vnd.smaf\": {\n        source: \"iana\",\n        extensions: [\n            \"mmf\"\n        ]\n    },\n    \"application/vnd.smart.teacher\": {\n        source: \"iana\",\n        extensions: [\n            \"teacher\"\n        ]\n    },\n    \"application/vnd.software602.filler.form+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"fo\"\n        ]\n    },\n    \"application/vnd.solent.sdkm+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"sdkm\",\n            \"sdkd\"\n        ]\n    },\n    \"application/vnd.spotfire.dxp\": {\n        source: \"iana\",\n        extensions: [\n            \"dxp\"\n        ]\n    },\n    \"application/vnd.spotfire.sfs\": {\n        source: \"iana\",\n        extensions: [\n            \"sfs\"\n        ]\n    },\n    \"application/vnd.stardivision.calc\": {\n        source: \"apache\",\n        extensions: [\n            \"sdc\"\n        ]\n    },\n    \"application/vnd.stardivision.draw\": {\n        source: \"apache\",\n        extensions: [\n            \"sda\"\n        ]\n    },\n    \"application/vnd.stardivision.impress\": {\n        source: \"apache\",\n        extensions: [\n            \"sdd\"\n        ]\n    },\n    \"application/vnd.stardivision.math\": {\n        source: \"apache\",\n        extensions: [\n            \"smf\"\n        ]\n    },\n    \"application/vnd.stardivision.writer\": {\n        source: \"apache\",\n        extensions: [\n            \"sdw\",\n            \"vor\"\n        ]\n    },\n    \"application/vnd.stardivision.writer-global\": {\n        source: \"apache\",\n        extensions: [\n            \"sgl\"\n        ]\n    },\n    \"application/vnd.stepmania.package\": {\n        source: \"iana\",\n        extensions: [\n            \"smzip\"\n        ]\n    },\n    \"application/vnd.stepmania.stepchart\": {\n        source: \"iana\",\n        extensions: [\n            \"sm\"\n        ]\n    },\n    \"application/vnd.sun.wadl+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"wadl\"\n        ]\n    },\n    \"application/vnd.sun.xml.calc\": {\n        source: \"apache\",\n        extensions: [\n            \"sxc\"\n        ]\n    },\n    \"application/vnd.sun.xml.calc.template\": {\n        source: \"apache\",\n        extensions: [\n            \"stc\"\n        ]\n    },\n    \"application/vnd.sun.xml.draw\": {\n        source: \"apache\",\n        extensions: [\n            \"sxd\"\n        ]\n    },\n    \"application/vnd.sun.xml.draw.template\": {\n        source: \"apache\",\n        extensions: [\n            \"std\"\n        ]\n    },\n    \"application/vnd.sun.xml.impress\": {\n        source: \"apache\",\n        extensions: [\n            \"sxi\"\n        ]\n    },\n    \"application/vnd.sun.xml.impress.template\": {\n        source: \"apache\",\n        extensions: [\n            \"sti\"\n        ]\n    },\n    \"application/vnd.sun.xml.math\": {\n        source: \"apache\",\n        extensions: [\n            \"sxm\"\n        ]\n    },\n    \"application/vnd.sun.xml.writer\": {\n        source: \"apache\",\n        extensions: [\n            \"sxw\"\n        ]\n    },\n    \"application/vnd.sun.xml.writer.global\": {\n        source: \"apache\",\n        extensions: [\n            \"sxg\"\n        ]\n    },\n    \"application/vnd.sun.xml.writer.template\": {\n        source: \"apache\",\n        extensions: [\n            \"stw\"\n        ]\n    },\n    \"application/vnd.sus-calendar\": {\n        source: \"iana\",\n        extensions: [\n            \"sus\",\n            \"susp\"\n        ]\n    },\n    \"application/vnd.svd\": {\n        source: \"iana\",\n        extensions: [\n            \"svd\"\n        ]\n    },\n    \"application/vnd.symbian.install\": {\n        source: \"apache\",\n        extensions: [\n            \"sis\",\n            \"sisx\"\n        ]\n    },\n    \"application/vnd.syncml+xml\": {\n        source: \"iana\",\n        charset: \"UTF-8\",\n        extensions: [\n            \"xsm\"\n        ]\n    },\n    \"application/vnd.syncml.dm+wbxml\": {\n        source: \"iana\",\n        charset: \"UTF-8\",\n        extensions: [\n            \"bdm\"\n        ]\n    },\n    \"application/vnd.syncml.dm+xml\": {\n        source: \"iana\",\n        charset: \"UTF-8\",\n        extensions: [\n            \"xdm\"\n        ]\n    },\n    \"application/vnd.syncml.dmddf+xml\": {\n        source: \"iana\",\n        charset: \"UTF-8\",\n        extensions: [\n            \"ddf\"\n        ]\n    },\n    \"application/vnd.tao.intent-module-archive\": {\n        source: \"iana\",\n        extensions: [\n            \"tao\"\n        ]\n    },\n    \"application/vnd.tcpdump.pcap\": {\n        source: \"iana\",\n        extensions: [\n            \"pcap\",\n            \"cap\",\n            \"dmp\"\n        ]\n    },\n    \"application/vnd.tmobile-livetv\": {\n        source: \"iana\",\n        extensions: [\n            \"tmo\"\n        ]\n    },\n    \"application/vnd.trid.tpt\": {\n        source: \"iana\",\n        extensions: [\n            \"tpt\"\n        ]\n    },\n    \"application/vnd.triscape.mxs\": {\n        source: \"iana\",\n        extensions: [\n            \"mxs\"\n        ]\n    },\n    \"application/vnd.trueapp\": {\n        source: \"iana\",\n        extensions: [\n            \"tra\"\n        ]\n    },\n    \"application/vnd.ufdl\": {\n        source: \"iana\",\n        extensions: [\n            \"ufd\",\n            \"ufdl\"\n        ]\n    },\n    \"application/vnd.uiq.theme\": {\n        source: \"iana\",\n        extensions: [\n            \"utz\"\n        ]\n    },\n    \"application/vnd.umajin\": {\n        source: \"iana\",\n        extensions: [\n            \"umj\"\n        ]\n    },\n    \"application/vnd.unity\": {\n        source: \"iana\",\n        extensions: [\n            \"unityweb\"\n        ]\n    },\n    \"application/vnd.uoml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"uoml\"\n        ]\n    },\n    \"application/vnd.vcx\": {\n        source: \"iana\",\n        extensions: [\n            \"vcx\"\n        ]\n    },\n    \"application/vnd.visio\": {\n        source: \"iana\",\n        extensions: [\n            \"vsd\",\n            \"vst\",\n            \"vss\",\n            \"vsw\"\n        ]\n    },\n    \"application/vnd.visionary\": {\n        source: \"iana\",\n        extensions: [\n            \"vis\"\n        ]\n    },\n    \"application/vnd.vsf\": {\n        source: \"iana\",\n        extensions: [\n            \"vsf\"\n        ]\n    },\n    \"application/vnd.wap.wbxml\": {\n        source: \"iana\",\n        charset: \"UTF-8\",\n        extensions: [\n            \"wbxml\"\n        ]\n    },\n    \"application/vnd.wap.wmlc\": {\n        source: \"iana\",\n        extensions: [\n            \"wmlc\"\n        ]\n    },\n    \"application/vnd.wap.wmlscriptc\": {\n        source: \"iana\",\n        extensions: [\n            \"wmlsc\"\n        ]\n    },\n    \"application/vnd.webturbo\": {\n        source: \"iana\",\n        extensions: [\n            \"wtb\"\n        ]\n    },\n    \"application/vnd.wolfram.player\": {\n        source: \"iana\",\n        extensions: [\n            \"nbp\"\n        ]\n    },\n    \"application/vnd.wordperfect\": {\n        source: \"iana\",\n        extensions: [\n            \"wpd\"\n        ]\n    },\n    \"application/vnd.wqd\": {\n        source: \"iana\",\n        extensions: [\n            \"wqd\"\n        ]\n    },\n    \"application/vnd.wt.stf\": {\n        source: \"iana\",\n        extensions: [\n            \"stf\"\n        ]\n    },\n    \"application/vnd.xara\": {\n        source: \"iana\",\n        extensions: [\n            \"xar\"\n        ]\n    },\n    \"application/vnd.xfdl\": {\n        source: \"iana\",\n        extensions: [\n            \"xfdl\"\n        ]\n    },\n    \"application/vnd.yamaha.hv-dic\": {\n        source: \"iana\",\n        extensions: [\n            \"hvd\"\n        ]\n    },\n    \"application/vnd.yamaha.hv-script\": {\n        source: \"iana\",\n        extensions: [\n            \"hvs\"\n        ]\n    },\n    \"application/vnd.yamaha.hv-voice\": {\n        source: \"iana\",\n        extensions: [\n            \"hvp\"\n        ]\n    },\n    \"application/vnd.yamaha.openscoreformat\": {\n        source: \"iana\",\n        extensions: [\n            \"osf\"\n        ]\n    },\n    \"application/vnd.yamaha.openscoreformat.osfpvg+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"osfpvg\"\n        ]\n    },\n    \"application/vnd.yamaha.smaf-audio\": {\n        source: \"iana\",\n        extensions: [\n            \"saf\"\n        ]\n    },\n    \"application/vnd.yamaha.smaf-phrase\": {\n        source: \"iana\",\n        extensions: [\n            \"spf\"\n        ]\n    },\n    \"application/vnd.yellowriver-custom-menu\": {\n        source: \"iana\",\n        extensions: [\n            \"cmp\"\n        ]\n    },\n    \"application/vnd.zul\": {\n        source: \"iana\",\n        extensions: [\n            \"zir\",\n            \"zirz\"\n        ]\n    },\n    \"application/vnd.zzazz.deck+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"zaz\"\n        ]\n    },\n    \"application/voicexml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"vxml\"\n        ]\n    },\n    \"application/wasm\": {\n        source: \"iana\",\n        extensions: [\n            \"wasm\"\n        ]\n    },\n    \"application/watcherinfo+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"wif\"\n        ]\n    },\n    \"application/widget\": {\n        source: \"iana\",\n        extensions: [\n            \"wgt\"\n        ]\n    },\n    \"application/winhlp\": {\n        source: \"apache\",\n        extensions: [\n            \"hlp\"\n        ]\n    },\n    \"application/wsdl+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"wsdl\"\n        ]\n    },\n    \"application/wspolicy+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"wspolicy\"\n        ]\n    },\n    \"application/x-7z-compressed\": {\n        source: \"apache\",\n        extensions: [\n            \"7z\"\n        ]\n    },\n    \"application/x-abiword\": {\n        source: \"apache\",\n        extensions: [\n            \"abw\"\n        ]\n    },\n    \"application/x-ace-compressed\": {\n        source: \"apache\",\n        extensions: [\n            \"ace\"\n        ]\n    },\n    \"application/x-apple-diskimage\": {\n        source: \"apache\",\n        extensions: [\n            \"dmg\"\n        ]\n    },\n    \"application/x-authorware-bin\": {\n        source: \"apache\",\n        extensions: [\n            \"aab\",\n            \"x32\",\n            \"u32\",\n            \"vox\"\n        ]\n    },\n    \"application/x-authorware-map\": {\n        source: \"apache\",\n        extensions: [\n            \"aam\"\n        ]\n    },\n    \"application/x-authorware-seg\": {\n        source: \"apache\",\n        extensions: [\n            \"aas\"\n        ]\n    },\n    \"application/x-bcpio\": {\n        source: \"apache\",\n        extensions: [\n            \"bcpio\"\n        ]\n    },\n    \"application/x-bittorrent\": {\n        source: \"apache\",\n        extensions: [\n            \"torrent\"\n        ]\n    },\n    \"application/x-blorb\": {\n        source: \"apache\",\n        extensions: [\n            \"blb\",\n            \"blorb\"\n        ]\n    },\n    \"application/x-bzip\": {\n        source: \"apache\",\n        extensions: [\n            \"bz\"\n        ]\n    },\n    \"application/x-bzip2\": {\n        source: \"apache\",\n        extensions: [\n            \"bz2\",\n            \"boz\"\n        ]\n    },\n    \"application/x-cbr\": {\n        source: \"apache\",\n        extensions: [\n            \"cbr\",\n            \"cba\",\n            \"cbt\",\n            \"cbz\",\n            \"cb7\"\n        ]\n    },\n    \"application/x-cdlink\": {\n        source: \"apache\",\n        extensions: [\n            \"vcd\"\n        ]\n    },\n    \"application/x-cfs-compressed\": {\n        source: \"apache\",\n        extensions: [\n            \"cfs\"\n        ]\n    },\n    \"application/x-chat\": {\n        source: \"apache\",\n        extensions: [\n            \"chat\"\n        ]\n    },\n    \"application/x-chess-pgn\": {\n        source: \"apache\",\n        extensions: [\n            \"pgn\"\n        ]\n    },\n    \"application/x-cocoa\": {\n        source: \"nginx\",\n        extensions: [\n            \"cco\"\n        ]\n    },\n    \"application/x-conference\": {\n        source: \"apache\",\n        extensions: [\n            \"nsc\"\n        ]\n    },\n    \"application/x-cpio\": {\n        source: \"apache\",\n        extensions: [\n            \"cpio\"\n        ]\n    },\n    \"application/x-csh\": {\n        source: \"apache\",\n        extensions: [\n            \"csh\"\n        ]\n    },\n    \"application/x-debian-package\": {\n        source: \"apache\",\n        extensions: [\n            \"deb\",\n            \"udeb\"\n        ]\n    },\n    \"application/x-dgc-compressed\": {\n        source: \"apache\",\n        extensions: [\n            \"dgc\"\n        ]\n    },\n    \"application/x-director\": {\n        source: \"apache\",\n        extensions: [\n            \"dir\",\n            \"dcr\",\n            \"dxr\",\n            \"cst\",\n            \"cct\",\n            \"cxt\",\n            \"w3d\",\n            \"fgd\",\n            \"swa\"\n        ]\n    },\n    \"application/x-doom\": {\n        source: \"apache\",\n        extensions: [\n            \"wad\"\n        ]\n    },\n    \"application/x-dtbncx+xml\": {\n        source: \"apache\",\n        extensions: [\n            \"ncx\"\n        ]\n    },\n    \"application/x-dtbook+xml\": {\n        source: \"apache\",\n        extensions: [\n            \"dtb\"\n        ]\n    },\n    \"application/x-dtbresource+xml\": {\n        source: \"apache\",\n        extensions: [\n            \"res\"\n        ]\n    },\n    \"application/x-dvi\": {\n        source: \"apache\",\n        extensions: [\n            \"dvi\"\n        ]\n    },\n    \"application/x-envoy\": {\n        source: \"apache\",\n        extensions: [\n            \"evy\"\n        ]\n    },\n    \"application/x-eva\": {\n        source: \"apache\",\n        extensions: [\n            \"eva\"\n        ]\n    },\n    \"application/x-font-bdf\": {\n        source: \"apache\",\n        extensions: [\n            \"bdf\"\n        ]\n    },\n    \"application/x-font-ghostscript\": {\n        source: \"apache\",\n        extensions: [\n            \"gsf\"\n        ]\n    },\n    \"application/x-font-linux-psf\": {\n        source: \"apache\",\n        extensions: [\n            \"psf\"\n        ]\n    },\n    \"application/x-font-pcf\": {\n        source: \"apache\",\n        extensions: [\n            \"pcf\"\n        ]\n    },\n    \"application/x-font-snf\": {\n        source: \"apache\",\n        extensions: [\n            \"snf\"\n        ]\n    },\n    \"application/x-font-type1\": {\n        source: \"apache\",\n        extensions: [\n            \"pfa\",\n            \"pfb\",\n            \"pfm\",\n            \"afm\"\n        ]\n    },\n    \"application/x-freearc\": {\n        source: \"apache\",\n        extensions: [\n            \"arc\"\n        ]\n    },\n    \"application/x-futuresplash\": {\n        source: \"apache\",\n        extensions: [\n            \"spl\"\n        ]\n    },\n    \"application/x-gca-compressed\": {\n        source: \"apache\",\n        extensions: [\n            \"gca\"\n        ]\n    },\n    \"application/x-glulx\": {\n        source: \"apache\",\n        extensions: [\n            \"ulx\"\n        ]\n    },\n    \"application/x-gnumeric\": {\n        source: \"apache\",\n        extensions: [\n            \"gnumeric\"\n        ]\n    },\n    \"application/x-gramps-xml\": {\n        source: \"apache\",\n        extensions: [\n            \"gramps\"\n        ]\n    },\n    \"application/x-gtar\": {\n        source: \"apache\",\n        extensions: [\n            \"gtar\"\n        ]\n    },\n    \"application/x-hdf\": {\n        source: \"apache\",\n        extensions: [\n            \"hdf\"\n        ]\n    },\n    \"application/x-install-instructions\": {\n        source: \"apache\",\n        extensions: [\n            \"install\"\n        ]\n    },\n    \"application/x-iso9660-image\": {\n        source: \"apache\",\n        extensions: [\n            \"iso\"\n        ]\n    },\n    \"application/x-java-archive-diff\": {\n        source: \"nginx\",\n        extensions: [\n            \"jardiff\"\n        ]\n    },\n    \"application/x-java-jnlp-file\": {\n        source: \"apache\",\n        extensions: [\n            \"jnlp\"\n        ]\n    },\n    \"application/x-latex\": {\n        source: \"apache\",\n        extensions: [\n            \"latex\"\n        ]\n    },\n    \"application/x-lzh-compressed\": {\n        source: \"apache\",\n        extensions: [\n            \"lzh\",\n            \"lha\"\n        ]\n    },\n    \"application/x-makeself\": {\n        source: \"nginx\",\n        extensions: [\n            \"run\"\n        ]\n    },\n    \"application/x-mie\": {\n        source: \"apache\",\n        extensions: [\n            \"mie\"\n        ]\n    },\n    \"application/x-mobipocket-ebook\": {\n        source: \"apache\",\n        extensions: [\n            \"prc\",\n            \"mobi\"\n        ]\n    },\n    \"application/x-ms-application\": {\n        source: \"apache\",\n        extensions: [\n            \"application\"\n        ]\n    },\n    \"application/x-ms-shortcut\": {\n        source: \"apache\",\n        extensions: [\n            \"lnk\"\n        ]\n    },\n    \"application/x-ms-wmd\": {\n        source: \"apache\",\n        extensions: [\n            \"wmd\"\n        ]\n    },\n    \"application/x-ms-wmz\": {\n        source: \"apache\",\n        extensions: [\n            \"wmz\"\n        ]\n    },\n    \"application/x-ms-xbap\": {\n        source: \"apache\",\n        extensions: [\n            \"xbap\"\n        ]\n    },\n    \"application/x-msaccess\": {\n        source: \"apache\",\n        extensions: [\n            \"mdb\"\n        ]\n    },\n    \"application/x-msbinder\": {\n        source: \"apache\",\n        extensions: [\n            \"obd\"\n        ]\n    },\n    \"application/x-mscardfile\": {\n        source: \"apache\",\n        extensions: [\n            \"crd\"\n        ]\n    },\n    \"application/x-msclip\": {\n        source: \"apache\",\n        extensions: [\n            \"clp\"\n        ]\n    },\n    \"application/x-msdownload\": {\n        source: \"apache\",\n        extensions: [\n            \"exe\",\n            \"dll\",\n            \"com\",\n            \"bat\",\n            \"msi\"\n        ]\n    },\n    \"application/x-msmediaview\": {\n        source: \"apache\",\n        extensions: [\n            \"mvb\",\n            \"m13\",\n            \"m14\"\n        ]\n    },\n    \"application/x-msmetafile\": {\n        source: \"apache\",\n        extensions: [\n            \"wmf\",\n            \"wmz\",\n            \"emf\",\n            \"emz\"\n        ]\n    },\n    \"application/x-msmoney\": {\n        source: \"apache\",\n        extensions: [\n            \"mny\"\n        ]\n    },\n    \"application/x-mspublisher\": {\n        source: \"apache\",\n        extensions: [\n            \"pub\"\n        ]\n    },\n    \"application/x-msschedule\": {\n        source: \"apache\",\n        extensions: [\n            \"scd\"\n        ]\n    },\n    \"application/x-msterminal\": {\n        source: \"apache\",\n        extensions: [\n            \"trm\"\n        ]\n    },\n    \"application/x-mswrite\": {\n        source: \"apache\",\n        extensions: [\n            \"wri\"\n        ]\n    },\n    \"application/x-netcdf\": {\n        source: \"apache\",\n        extensions: [\n            \"nc\",\n            \"cdf\"\n        ]\n    },\n    \"application/x-nzb\": {\n        source: \"apache\",\n        extensions: [\n            \"nzb\"\n        ]\n    },\n    \"application/x-perl\": {\n        source: \"nginx\",\n        extensions: [\n            \"pl\",\n            \"pm\"\n        ]\n    },\n    \"application/x-pilot\": {\n        source: \"nginx\",\n        extensions: [\n            \"prc\",\n            \"pdb\"\n        ]\n    },\n    \"application/x-pkcs12\": {\n        source: \"apache\",\n        extensions: [\n            \"p12\",\n            \"pfx\"\n        ]\n    },\n    \"application/x-pkcs7-certificates\": {\n        source: \"apache\",\n        extensions: [\n            \"p7b\",\n            \"spc\"\n        ]\n    },\n    \"application/x-pkcs7-certreqresp\": {\n        source: \"apache\",\n        extensions: [\n            \"p7r\"\n        ]\n    },\n    \"application/x-rar-compressed\": {\n        source: \"apache\",\n        extensions: [\n            \"rar\"\n        ]\n    },\n    \"application/x-redhat-package-manager\": {\n        source: \"nginx\",\n        extensions: [\n            \"rpm\"\n        ]\n    },\n    \"application/x-research-info-systems\": {\n        source: \"apache\",\n        extensions: [\n            \"ris\"\n        ]\n    },\n    \"application/x-sea\": {\n        source: \"nginx\",\n        extensions: [\n            \"sea\"\n        ]\n    },\n    \"application/x-sh\": {\n        source: \"apache\",\n        extensions: [\n            \"sh\"\n        ]\n    },\n    \"application/x-shar\": {\n        source: \"apache\",\n        extensions: [\n            \"shar\"\n        ]\n    },\n    \"application/x-shockwave-flash\": {\n        source: \"apache\",\n        extensions: [\n            \"swf\"\n        ]\n    },\n    \"application/x-silverlight-app\": {\n        source: \"apache\",\n        extensions: [\n            \"xap\"\n        ]\n    },\n    \"application/x-sql\": {\n        source: \"apache\",\n        extensions: [\n            \"sql\"\n        ]\n    },\n    \"application/x-stuffit\": {\n        source: \"apache\",\n        extensions: [\n            \"sit\"\n        ]\n    },\n    \"application/x-stuffitx\": {\n        source: \"apache\",\n        extensions: [\n            \"sitx\"\n        ]\n    },\n    \"application/x-subrip\": {\n        source: \"apache\",\n        extensions: [\n            \"srt\"\n        ]\n    },\n    \"application/x-sv4cpio\": {\n        source: \"apache\",\n        extensions: [\n            \"sv4cpio\"\n        ]\n    },\n    \"application/x-sv4crc\": {\n        source: \"apache\",\n        extensions: [\n            \"sv4crc\"\n        ]\n    },\n    \"application/x-t3vm-image\": {\n        source: \"apache\",\n        extensions: [\n            \"t3\"\n        ]\n    },\n    \"application/x-tads\": {\n        source: \"apache\",\n        extensions: [\n            \"gam\"\n        ]\n    },\n    \"application/x-tar\": {\n        source: \"apache\",\n        extensions: [\n            \"tar\"\n        ]\n    },\n    \"application/x-tcl\": {\n        source: \"apache\",\n        extensions: [\n            \"tcl\",\n            \"tk\"\n        ]\n    },\n    \"application/x-tex\": {\n        source: \"apache\",\n        extensions: [\n            \"tex\"\n        ]\n    },\n    \"application/x-tex-tfm\": {\n        source: \"apache\",\n        extensions: [\n            \"tfm\"\n        ]\n    },\n    \"application/x-texinfo\": {\n        source: \"apache\",\n        extensions: [\n            \"texinfo\",\n            \"texi\"\n        ]\n    },\n    \"application/x-tgif\": {\n        source: \"apache\",\n        extensions: [\n            \"obj\"\n        ]\n    },\n    \"application/x-ustar\": {\n        source: \"apache\",\n        extensions: [\n            \"ustar\"\n        ]\n    },\n    \"application/x-wais-source\": {\n        source: \"apache\",\n        extensions: [\n            \"src\"\n        ]\n    },\n    \"application/x-x509-ca-cert\": {\n        source: \"iana\",\n        extensions: [\n            \"der\",\n            \"crt\",\n            \"pem\"\n        ]\n    },\n    \"application/x-xfig\": {\n        source: \"apache\",\n        extensions: [\n            \"fig\"\n        ]\n    },\n    \"application/x-xliff+xml\": {\n        source: \"apache\",\n        extensions: [\n            \"xlf\"\n        ]\n    },\n    \"application/x-xpinstall\": {\n        source: \"apache\",\n        extensions: [\n            \"xpi\"\n        ]\n    },\n    \"application/x-xz\": {\n        source: \"apache\",\n        extensions: [\n            \"xz\"\n        ]\n    },\n    \"application/x-zmachine\": {\n        source: \"apache\",\n        extensions: [\n            \"z1\",\n            \"z2\",\n            \"z3\",\n            \"z4\",\n            \"z5\",\n            \"z6\",\n            \"z7\",\n            \"z8\"\n        ]\n    },\n    \"application/xaml+xml\": {\n        source: \"apache\",\n        extensions: [\n            \"xaml\"\n        ]\n    },\n    \"application/xcap-att+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xav\"\n        ]\n    },\n    \"application/xcap-caps+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xca\"\n        ]\n    },\n    \"application/xcap-diff+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xdf\"\n        ]\n    },\n    \"application/xcap-el+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xel\"\n        ]\n    },\n    \"application/xcap-ns+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xns\"\n        ]\n    },\n    \"application/xenc+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xenc\"\n        ]\n    },\n    \"application/xhtml+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xhtml\",\n            \"xht\"\n        ]\n    },\n    \"application/xliff+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xlf\"\n        ]\n    },\n    \"application/xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xml\",\n            \"xsl\",\n            \"xsd\",\n            \"rng\"\n        ]\n    },\n    \"application/xml-dtd\": {\n        source: \"iana\",\n        extensions: [\n            \"dtd\"\n        ]\n    },\n    \"application/xop+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xop\"\n        ]\n    },\n    \"application/xproc+xml\": {\n        source: \"apache\",\n        extensions: [\n            \"xpl\"\n        ]\n    },\n    \"application/xslt+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xsl\",\n            \"xslt\"\n        ]\n    },\n    \"application/xspf+xml\": {\n        source: \"apache\",\n        extensions: [\n            \"xspf\"\n        ]\n    },\n    \"application/xv+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"mxml\",\n            \"xhvml\",\n            \"xvml\",\n            \"xvm\"\n        ]\n    },\n    \"application/yaml\": {\n        source: \"iana\",\n        extensions: [\n            \"yaml\",\n            \"yml\"\n        ]\n    },\n    \"application/yang\": {\n        source: \"iana\",\n        extensions: [\n            \"yang\"\n        ]\n    },\n    \"application/yin+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"yin\"\n        ]\n    },\n    \"application/zip\": {\n        source: \"iana\",\n        extensions: [\n            \"zip\"\n        ]\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@uploadthing/mime-types/application/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@uploadthing/mime-types/audio/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/@uploadthing/mime-types/audio/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   audio: () => (/* binding */ audio)\n/* harmony export */ });\nconst audio = {\n    \"audio/3gpp\": {\n        source: \"iana\",\n        extensions: [\n            \"3gpp\"\n        ]\n    },\n    \"audio/adpcm\": {\n        source: \"apache\",\n        extensions: [\n            \"adp\"\n        ]\n    },\n    \"audio/amr\": {\n        source: \"iana\",\n        extensions: [\n            \"amr\"\n        ]\n    },\n    \"audio/basic\": {\n        source: \"iana\",\n        extensions: [\n            \"au\",\n            \"snd\"\n        ]\n    },\n    \"audio/midi\": {\n        source: \"apache\",\n        extensions: [\n            \"mid\",\n            \"midi\",\n            \"kar\",\n            \"rmi\"\n        ]\n    },\n    \"audio/mobile-xmf\": {\n        source: \"iana\",\n        extensions: [\n            \"mxmf\"\n        ]\n    },\n    \"audio/mp4\": {\n        source: \"iana\",\n        extensions: [\n            \"m4a\",\n            \"mp4a\"\n        ]\n    },\n    \"audio/mpeg\": {\n        source: \"iana\",\n        extensions: [\n            \"mpga\",\n            \"mp2\",\n            \"mp2a\",\n            \"mp3\",\n            \"m2a\",\n            \"m3a\"\n        ]\n    },\n    \"audio/ogg\": {\n        source: \"iana\",\n        extensions: [\n            \"oga\",\n            \"ogg\",\n            \"spx\",\n            \"opus\"\n        ]\n    },\n    \"audio/s3m\": {\n        source: \"apache\",\n        extensions: [\n            \"s3m\"\n        ]\n    },\n    \"audio/silk\": {\n        source: \"apache\",\n        extensions: [\n            \"sil\"\n        ]\n    },\n    \"audio/vnd.dece.audio\": {\n        source: \"iana\",\n        extensions: [\n            \"uva\",\n            \"uvva\"\n        ]\n    },\n    \"audio/vnd.digital-winds\": {\n        source: \"iana\",\n        extensions: [\n            \"eol\"\n        ]\n    },\n    \"audio/vnd.dra\": {\n        source: \"iana\",\n        extensions: [\n            \"dra\"\n        ]\n    },\n    \"audio/vnd.dts\": {\n        source: \"iana\",\n        extensions: [\n            \"dts\"\n        ]\n    },\n    \"audio/vnd.dts.hd\": {\n        source: \"iana\",\n        extensions: [\n            \"dtshd\"\n        ]\n    },\n    \"audio/vnd.lucent.voice\": {\n        source: \"iana\",\n        extensions: [\n            \"lvp\"\n        ]\n    },\n    \"audio/vnd.ms-playready.media.pya\": {\n        source: \"iana\",\n        extensions: [\n            \"pya\"\n        ]\n    },\n    \"audio/vnd.nuera.ecelp4800\": {\n        source: \"iana\",\n        extensions: [\n            \"ecelp4800\"\n        ]\n    },\n    \"audio/vnd.nuera.ecelp7470\": {\n        source: \"iana\",\n        extensions: [\n            \"ecelp7470\"\n        ]\n    },\n    \"audio/vnd.nuera.ecelp9600\": {\n        source: \"iana\",\n        extensions: [\n            \"ecelp9600\"\n        ]\n    },\n    \"audio/vnd.rip\": {\n        source: \"iana\",\n        extensions: [\n            \"rip\"\n        ]\n    },\n    \"audio/webm\": {\n        source: \"apache\",\n        extensions: [\n            \"weba\"\n        ]\n    },\n    \"audio/x-aac\": {\n        source: \"apache\",\n        extensions: [\n            \"aac\"\n        ]\n    },\n    \"audio/x-aiff\": {\n        source: \"apache\",\n        extensions: [\n            \"aif\",\n            \"aiff\",\n            \"aifc\"\n        ]\n    },\n    \"audio/x-caf\": {\n        source: \"apache\",\n        extensions: [\n            \"caf\"\n        ]\n    },\n    \"audio/x-flac\": {\n        source: \"apache\",\n        extensions: [\n            \"flac\"\n        ]\n    },\n    \"audio/x-m4a\": {\n        source: \"nginx\",\n        extensions: [\n            \"m4a\"\n        ]\n    },\n    \"audio/x-matroska\": {\n        source: \"apache\",\n        extensions: [\n            \"mka\"\n        ]\n    },\n    \"audio/x-mpegurl\": {\n        source: \"apache\",\n        extensions: [\n            \"m3u\"\n        ]\n    },\n    \"audio/x-ms-wax\": {\n        source: \"apache\",\n        extensions: [\n            \"wax\"\n        ]\n    },\n    \"audio/x-ms-wma\": {\n        source: \"apache\",\n        extensions: [\n            \"wma\"\n        ]\n    },\n    \"audio/x-pn-realaudio\": {\n        source: \"apache\",\n        extensions: [\n            \"ram\",\n            \"ra\"\n        ]\n    },\n    \"audio/x-pn-realaudio-plugin\": {\n        source: \"apache\",\n        extensions: [\n            \"rmp\"\n        ]\n    },\n    \"audio/x-realaudio\": {\n        source: \"nginx\",\n        extensions: [\n            \"ra\"\n        ]\n    },\n    \"audio/x-wav\": {\n        source: \"apache\",\n        extensions: [\n            \"wav\"\n        ]\n    },\n    \"audio/x-gsm\": {\n        source: \"apache\",\n        extensions: [\n            \"gsm\"\n        ]\n    },\n    \"audio/xm\": {\n        source: \"apache\",\n        extensions: [\n            \"xm\"\n        ]\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@uploadthing/mime-types/audio/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@uploadthing/mime-types/dist/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@uploadthing/mime-types/dist/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getExtensions: () => (/* binding */ getExtensions),\n/* harmony export */   getTypes: () => (/* binding */ getTypes),\n/* harmony export */   lookup: () => (/* binding */ lookup),\n/* harmony export */   mimeTypes: () => (/* binding */ mimeTypes)\n/* harmony export */ });\n/* harmony import */ var _application_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../application/index.js */ \"(rsc)/./node_modules/@uploadthing/mime-types/application/index.js\");\n/* harmony import */ var _audio_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../audio/index.js */ \"(rsc)/./node_modules/@uploadthing/mime-types/audio/index.js\");\n/* harmony import */ var _image_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../image/index.js */ \"(rsc)/./node_modules/@uploadthing/mime-types/image/index.js\");\n/* harmony import */ var _text_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../text/index.js */ \"(rsc)/./node_modules/@uploadthing/mime-types/text/index.js\");\n/* harmony import */ var _video_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../video/index.js */ \"(rsc)/./node_modules/@uploadthing/mime-types/video/index.js\");\n\n\n\n\n\n\n/**\n * Random types not worthy of their own file\n */ const misc = {\n    \"chemical/x-cdx\": {\n        source: \"apache\",\n        extensions: [\n            \"cdx\"\n        ]\n    },\n    \"chemical/x-cif\": {\n        source: \"apache\",\n        extensions: [\n            \"cif\"\n        ]\n    },\n    \"chemical/x-cmdf\": {\n        source: \"apache\",\n        extensions: [\n            \"cmdf\"\n        ]\n    },\n    \"chemical/x-cml\": {\n        source: \"apache\",\n        extensions: [\n            \"cml\"\n        ]\n    },\n    \"chemical/x-csml\": {\n        source: \"apache\",\n        extensions: [\n            \"csml\"\n        ]\n    },\n    \"chemical/x-xyz\": {\n        source: \"apache\",\n        extensions: [\n            \"xyz\"\n        ]\n    },\n    \"font/collection\": {\n        source: \"iana\",\n        extensions: [\n            \"ttc\"\n        ]\n    },\n    \"font/otf\": {\n        source: \"iana\",\n        extensions: [\n            \"otf\"\n        ]\n    },\n    \"font/ttf\": {\n        source: \"iana\",\n        extensions: [\n            \"ttf\"\n        ]\n    },\n    \"font/woff\": {\n        source: \"iana\",\n        extensions: [\n            \"woff\"\n        ]\n    },\n    \"font/woff2\": {\n        source: \"iana\",\n        extensions: [\n            \"woff2\"\n        ]\n    },\n    \"message/disposition-notification\": {\n        source: \"iana\",\n        extensions: [\n            \"disposition-notification\"\n        ]\n    },\n    \"message/global\": {\n        source: \"iana\",\n        extensions: [\n            \"u8msg\"\n        ]\n    },\n    \"message/global-delivery-status\": {\n        source: \"iana\",\n        extensions: [\n            \"u8dsn\"\n        ]\n    },\n    \"message/global-disposition-notification\": {\n        source: \"iana\",\n        extensions: [\n            \"u8mdn\"\n        ]\n    },\n    \"message/global-headers\": {\n        source: \"iana\",\n        extensions: [\n            \"u8hdr\"\n        ]\n    },\n    \"message/rfc822\": {\n        source: \"iana\",\n        extensions: [\n            \"eml\",\n            \"mime\"\n        ]\n    },\n    \"message/vnd.wfa.wsc\": {\n        source: \"iana\",\n        extensions: [\n            \"wsc\"\n        ]\n    },\n    \"model/3mf\": {\n        source: \"iana\",\n        extensions: [\n            \"3mf\"\n        ]\n    },\n    \"model/gltf+json\": {\n        source: \"iana\",\n        extensions: [\n            \"gltf\"\n        ]\n    },\n    \"model/gltf-binary\": {\n        source: \"iana\",\n        extensions: [\n            \"glb\"\n        ]\n    },\n    \"model/iges\": {\n        source: \"iana\",\n        extensions: [\n            \"igs\",\n            \"iges\"\n        ]\n    },\n    \"model/mesh\": {\n        source: \"iana\",\n        extensions: [\n            \"msh\",\n            \"mesh\",\n            \"silo\"\n        ]\n    },\n    \"model/mtl\": {\n        source: \"iana\",\n        extensions: [\n            \"mtl\"\n        ]\n    },\n    \"model/obj\": {\n        source: \"iana\",\n        extensions: [\n            \"obj\"\n        ]\n    },\n    \"model/step\": {\n        source: \"iana\",\n        extensions: [\n            \".p21\",\n            \".stp\",\n            \".step\",\n            \".stpnc\",\n            \".210\"\n        ]\n    },\n    \"model/step+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"stpx\"\n        ]\n    },\n    \"model/step+zip\": {\n        source: \"iana\",\n        extensions: [\n            \"stpz\"\n        ]\n    },\n    \"model/step-xml+zip\": {\n        source: \"iana\",\n        extensions: [\n            \"stpxz\"\n        ]\n    },\n    \"model/stl\": {\n        source: \"iana\",\n        extensions: [\n            \"stl\"\n        ]\n    },\n    \"model/vnd.collada+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"dae\"\n        ]\n    },\n    \"model/vnd.dwf\": {\n        source: \"iana\",\n        extensions: [\n            \"dwf\"\n        ]\n    },\n    \"model/vnd.gdl\": {\n        source: \"iana\",\n        extensions: [\n            \"gdl\"\n        ]\n    },\n    \"model/vnd.gtw\": {\n        source: \"iana\",\n        extensions: [\n            \"gtw\"\n        ]\n    },\n    \"model/vnd.mts\": {\n        source: \"iana\",\n        extensions: [\n            \"mts\"\n        ]\n    },\n    \"model/vnd.opengex\": {\n        source: \"iana\",\n        extensions: [\n            \"ogex\"\n        ]\n    },\n    \"model/vnd.parasolid.transmit.binary\": {\n        source: \"iana\",\n        extensions: [\n            \"x_b\"\n        ]\n    },\n    \"model/vnd.parasolid.transmit.text\": {\n        source: \"iana\",\n        extensions: [\n            \"x_t\"\n        ]\n    },\n    \"model/vnd.sap.vds\": {\n        source: \"iana\",\n        extensions: [\n            \"vds\"\n        ]\n    },\n    \"model/vnd.usdz+zip\": {\n        source: \"iana\",\n        extensions: [\n            \"usdz\"\n        ]\n    },\n    \"model/vnd.valve.source.compiled-map\": {\n        source: \"iana\",\n        extensions: [\n            \"bsp\"\n        ]\n    },\n    \"model/vnd.vtu\": {\n        source: \"iana\",\n        extensions: [\n            \"vtu\"\n        ]\n    },\n    \"model/vrml\": {\n        source: \"iana\",\n        extensions: [\n            \"wrl\",\n            \"vrml\"\n        ]\n    },\n    \"model/x3d+binary\": {\n        source: \"apache\",\n        extensions: [\n            \"x3db\",\n            \"x3dbz\"\n        ]\n    },\n    \"model/x3d+fastinfoset\": {\n        source: \"iana\",\n        extensions: [\n            \"x3db\"\n        ]\n    },\n    \"model/x3d+vrml\": {\n        source: \"apache\",\n        extensions: [\n            \"x3dv\",\n            \"x3dvz\"\n        ]\n    },\n    \"model/x3d+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"x3d\",\n            \"x3dz\"\n        ]\n    },\n    \"model/x3d-vrml\": {\n        source: \"iana\",\n        extensions: [\n            \"x3dv\"\n        ]\n    },\n    \"x-conference/x-cooltalk\": {\n        source: \"apache\",\n        extensions: [\n            \"ice\"\n        ]\n    }\n};\n\nconst mimes = {\n    ..._application_index_js__WEBPACK_IMPORTED_MODULE_0__.application,\n    ..._audio_index_js__WEBPACK_IMPORTED_MODULE_1__.audio,\n    ..._image_index_js__WEBPACK_IMPORTED_MODULE_2__.image,\n    ..._text_index_js__WEBPACK_IMPORTED_MODULE_3__.text,\n    ..._video_index_js__WEBPACK_IMPORTED_MODULE_4__.video,\n    ...misc\n};\nconst mimeTypes = mimes;\nfunction extname(path) {\n    const index = path.lastIndexOf(\".\");\n    return index < 0 ? \"\" : path.substring(index);\n}\nconst extensions = {};\nconst types = {};\n// Introduce getters to improve tree-shakeability\nfunction getTypes() {\n    populateMaps(extensions, types);\n    return types;\n}\nfunction getExtensions() {\n    populateMaps(extensions, types);\n    return extensions;\n}\n/**\n * Lookup the MIME type for a file path/extension.\n */ function lookup(path) {\n    if (!path || typeof path !== \"string\") {\n        return false;\n    }\n    // get the extension (\"ext\" or \".ext\" or full path)\n    const extension = extname(\"x.\" + path).toLowerCase().substring(1);\n    if (!extension) {\n        return false;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    return getTypes()[extension] || false;\n}\nlet inittedMaps = false;\n/**\n * Populate the extensions and types maps.\n * @private\n */ function populateMaps(extensions, types) {\n    if (inittedMaps) return;\n    inittedMaps = true;\n    // source preference (least -> most)\n    const preference = [\n        \"nginx\",\n        \"apache\",\n        undefined,\n        \"iana\"\n    ];\n    Object.keys(mimeTypes).forEach((type)=>{\n        const mime = mimeTypes[type];\n        const exts = mime.extensions;\n        if (!exts.length) {\n            return;\n        }\n        // mime -> extensions\n        extensions[type] = exts;\n        // extension -> mime\n        for (const extension of exts){\n            if (extension in types) {\n                const from = preference.indexOf(mimeTypes[types[extension]].source);\n                const to = preference.indexOf(mime.source);\n                if (types[extension] !== \"application/octet-stream\" && (from > to || from === to && types[extension].startsWith(\"application/\"))) {\n                    continue;\n                }\n            }\n            // set the extension -> mime\n            types[extension] = type;\n        }\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@uploadthing/mime-types/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@uploadthing/mime-types/image/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/@uploadthing/mime-types/image/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   image: () => (/* binding */ image)\n/* harmony export */ });\nconst image = {\n    \"image/aces\": {\n        source: \"iana\",\n        extensions: [\n            \"exr\"\n        ]\n    },\n    \"image/avci\": {\n        source: \"iana\",\n        extensions: [\n            \"avci\"\n        ]\n    },\n    \"image/avcs\": {\n        source: \"iana\",\n        extensions: [\n            \"avcs\"\n        ]\n    },\n    \"image/avif\": {\n        source: \"iana\",\n        extensions: [\n            \"avif\"\n        ]\n    },\n    \"image/bmp\": {\n        source: \"iana\",\n        extensions: [\n            \"bmp\"\n        ]\n    },\n    \"image/cgm\": {\n        source: \"iana\",\n        extensions: [\n            \"cgm\"\n        ]\n    },\n    \"image/dicom-rle\": {\n        source: \"iana\",\n        extensions: [\n            \"drle\"\n        ]\n    },\n    \"image/emf\": {\n        source: \"iana\",\n        extensions: [\n            \"emf\"\n        ]\n    },\n    \"image/fits\": {\n        source: \"iana\",\n        extensions: [\n            \"fits\"\n        ]\n    },\n    \"image/g3fax\": {\n        source: \"iana\",\n        extensions: [\n            \"g3\"\n        ]\n    },\n    \"image/gif\": {\n        source: \"iana\",\n        extensions: [\n            \"gif\"\n        ]\n    },\n    \"image/heic\": {\n        source: \"iana\",\n        extensions: [\n            \"heic\"\n        ]\n    },\n    \"image/heic-sequence\": {\n        source: \"iana\",\n        extensions: [\n            \"heics\"\n        ]\n    },\n    \"image/heif\": {\n        source: \"iana\",\n        extensions: [\n            \"heif\"\n        ]\n    },\n    \"image/heif-sequence\": {\n        source: \"iana\",\n        extensions: [\n            \"heifs\"\n        ]\n    },\n    \"image/hej2k\": {\n        source: \"iana\",\n        extensions: [\n            \"hej2\"\n        ]\n    },\n    \"image/hsj2\": {\n        source: \"iana\",\n        extensions: [\n            \"hsj2\"\n        ]\n    },\n    \"image/ief\": {\n        source: \"iana\",\n        extensions: [\n            \"ief\"\n        ]\n    },\n    \"image/jls\": {\n        source: \"iana\",\n        extensions: [\n            \"jls\"\n        ]\n    },\n    \"image/jp2\": {\n        source: \"iana\",\n        extensions: [\n            \"jp2\",\n            \"jpg2\"\n        ]\n    },\n    \"image/jpeg\": {\n        source: \"iana\",\n        extensions: [\n            \"jpeg\",\n            \"jpg\",\n            \"jpe\",\n            \"jfif\",\n            \"pjpeg\",\n            \"pjp\"\n        ]\n    },\n    \"image/jph\": {\n        source: \"iana\",\n        extensions: [\n            \"jph\"\n        ]\n    },\n    \"image/jphc\": {\n        source: \"iana\",\n        extensions: [\n            \"jhc\"\n        ]\n    },\n    \"image/jpm\": {\n        source: \"iana\",\n        extensions: [\n            \"jpm\"\n        ]\n    },\n    \"image/jpx\": {\n        source: \"iana\",\n        extensions: [\n            \"jpx\",\n            \"jpf\"\n        ]\n    },\n    \"image/jxr\": {\n        source: \"iana\",\n        extensions: [\n            \"jxr\"\n        ]\n    },\n    \"image/jxra\": {\n        source: \"iana\",\n        extensions: [\n            \"jxra\"\n        ]\n    },\n    \"image/jxrs\": {\n        source: \"iana\",\n        extensions: [\n            \"jxrs\"\n        ]\n    },\n    \"image/jxs\": {\n        source: \"iana\",\n        extensions: [\n            \"jxs\"\n        ]\n    },\n    \"image/jxsc\": {\n        source: \"iana\",\n        extensions: [\n            \"jxsc\"\n        ]\n    },\n    \"image/jxsi\": {\n        source: \"iana\",\n        extensions: [\n            \"jxsi\"\n        ]\n    },\n    \"image/jxss\": {\n        source: \"iana\",\n        extensions: [\n            \"jxss\"\n        ]\n    },\n    \"image/ktx\": {\n        source: \"iana\",\n        extensions: [\n            \"ktx\"\n        ]\n    },\n    \"image/ktx2\": {\n        source: \"iana\",\n        extensions: [\n            \"ktx2\"\n        ]\n    },\n    \"image/png\": {\n        source: \"iana\",\n        extensions: [\n            \"png\"\n        ]\n    },\n    \"image/prs.btif\": {\n        source: \"iana\",\n        extensions: [\n            \"btif\"\n        ]\n    },\n    \"image/prs.pti\": {\n        source: \"iana\",\n        extensions: [\n            \"pti\"\n        ]\n    },\n    \"image/sgi\": {\n        source: \"apache\",\n        extensions: [\n            \"sgi\"\n        ]\n    },\n    \"image/svg+xml\": {\n        source: \"iana\",\n        extensions: [\n            \"svg\",\n            \"svgz\"\n        ]\n    },\n    \"image/t38\": {\n        source: \"iana\",\n        extensions: [\n            \"t38\"\n        ]\n    },\n    \"image/tiff\": {\n        source: \"iana\",\n        extensions: [\n            \"tif\",\n            \"tiff\"\n        ]\n    },\n    \"image/tiff-fx\": {\n        source: \"iana\",\n        extensions: [\n            \"tfx\"\n        ]\n    },\n    \"image/vnd.adobe.photoshop\": {\n        source: \"iana\",\n        extensions: [\n            \"psd\"\n        ]\n    },\n    \"image/vnd.airzip.accelerator.azv\": {\n        source: \"iana\",\n        extensions: [\n            \"azv\"\n        ]\n    },\n    \"image/vnd.dece.graphic\": {\n        source: \"iana\",\n        extensions: [\n            \"uvi\",\n            \"uvvi\",\n            \"uvg\",\n            \"uvvg\"\n        ]\n    },\n    \"image/vnd.djvu\": {\n        source: \"iana\",\n        extensions: [\n            \"djvu\",\n            \"djv\"\n        ]\n    },\n    \"image/vnd.dvb.subtitle\": {\n        source: \"iana\",\n        extensions: [\n            \"sub\"\n        ]\n    },\n    \"image/vnd.dwg\": {\n        source: \"iana\",\n        extensions: [\n            \"dwg\"\n        ]\n    },\n    \"image/vnd.dxf\": {\n        source: \"iana\",\n        extensions: [\n            \"dxf\"\n        ]\n    },\n    \"image/vnd.fastbidsheet\": {\n        source: \"iana\",\n        extensions: [\n            \"fbs\"\n        ]\n    },\n    \"image/vnd.fpx\": {\n        source: \"iana\",\n        extensions: [\n            \"fpx\"\n        ]\n    },\n    \"image/vnd.fst\": {\n        source: \"iana\",\n        extensions: [\n            \"fst\"\n        ]\n    },\n    \"image/vnd.fujixerox.edmics-mmr\": {\n        source: \"iana\",\n        extensions: [\n            \"mmr\"\n        ]\n    },\n    \"image/vnd.fujixerox.edmics-rlc\": {\n        source: \"iana\",\n        extensions: [\n            \"rlc\"\n        ]\n    },\n    \"image/vnd.microsoft.icon\": {\n        source: \"iana\",\n        extensions: [\n            \"ico\"\n        ]\n    },\n    \"image/vnd.ms-modi\": {\n        source: \"iana\",\n        extensions: [\n            \"mdi\"\n        ]\n    },\n    \"image/vnd.ms-photo\": {\n        source: \"apache\",\n        extensions: [\n            \"wdp\"\n        ]\n    },\n    \"image/vnd.net-fpx\": {\n        source: \"iana\",\n        extensions: [\n            \"npx\"\n        ]\n    },\n    \"image/vnd.pco.b16\": {\n        source: \"iana\",\n        extensions: [\n            \"b16\"\n        ]\n    },\n    \"image/vnd.tencent.tap\": {\n        source: \"iana\",\n        extensions: [\n            \"tap\"\n        ]\n    },\n    \"image/vnd.valve.source.texture\": {\n        source: \"iana\",\n        extensions: [\n            \"vtf\"\n        ]\n    },\n    \"image/vnd.wap.wbmp\": {\n        source: \"iana\",\n        extensions: [\n            \"wbmp\"\n        ]\n    },\n    \"image/vnd.xiff\": {\n        source: \"iana\",\n        extensions: [\n            \"xif\"\n        ]\n    },\n    \"image/vnd.zbrush.pcx\": {\n        source: \"iana\",\n        extensions: [\n            \"pcx\"\n        ]\n    },\n    \"image/webp\": {\n        source: \"apache\",\n        extensions: [\n            \"webp\"\n        ]\n    },\n    \"image/wmf\": {\n        source: \"iana\",\n        extensions: [\n            \"wmf\"\n        ]\n    },\n    \"image/x-3ds\": {\n        source: \"apache\",\n        extensions: [\n            \"3ds\"\n        ]\n    },\n    \"image/x-cmu-raster\": {\n        source: \"apache\",\n        extensions: [\n            \"ras\"\n        ]\n    },\n    \"image/x-cmx\": {\n        source: \"apache\",\n        extensions: [\n            \"cmx\"\n        ]\n    },\n    \"image/x-freehand\": {\n        source: \"apache\",\n        extensions: [\n            \"fh\",\n            \"fhc\",\n            \"fh4\",\n            \"fh5\",\n            \"fh7\"\n        ]\n    },\n    \"image/x-icon\": {\n        source: \"apache\",\n        extensions: [\n            \"ico\"\n        ]\n    },\n    \"image/x-jng\": {\n        source: \"nginx\",\n        extensions: [\n            \"jng\"\n        ]\n    },\n    \"image/x-mrsid-image\": {\n        source: \"apache\",\n        extensions: [\n            \"sid\"\n        ]\n    },\n    \"image/x-ms-bmp\": {\n        source: \"nginx\",\n        extensions: [\n            \"bmp\"\n        ]\n    },\n    \"image/x-pcx\": {\n        source: \"apache\",\n        extensions: [\n            \"pcx\"\n        ]\n    },\n    \"image/x-pict\": {\n        source: \"apache\",\n        extensions: [\n            \"pic\",\n            \"pct\"\n        ]\n    },\n    \"image/x-portable-anymap\": {\n        source: \"apache\",\n        extensions: [\n            \"pnm\"\n        ]\n    },\n    \"image/x-portable-bitmap\": {\n        source: \"apache\",\n        extensions: [\n            \"pbm\"\n        ]\n    },\n    \"image/x-portable-graymap\": {\n        source: \"apache\",\n        extensions: [\n            \"pgm\"\n        ]\n    },\n    \"image/x-portable-pixmap\": {\n        source: \"apache\",\n        extensions: [\n            \"ppm\"\n        ]\n    },\n    \"image/x-rgb\": {\n        source: \"apache\",\n        extensions: [\n            \"rgb\"\n        ]\n    },\n    \"image/x-tga\": {\n        source: \"apache\",\n        extensions: [\n            \"tga\"\n        ]\n    },\n    \"image/x-xbitmap\": {\n        source: \"apache\",\n        extensions: [\n            \"xbm\"\n        ]\n    },\n    \"image/x-xpixmap\": {\n        source: \"apache\",\n        extensions: [\n            \"xpm\"\n        ]\n    },\n    \"image/x-xwindowdump\": {\n        source: \"apache\",\n        extensions: [\n            \"xwd\"\n        ]\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@uploadthing/mime-types/image/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@uploadthing/mime-types/text/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@uploadthing/mime-types/text/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\nconst text = {\n    \"text/cache-manifest\": {\n        source: \"iana\",\n        extensions: [\n            \"appcache\",\n            \"manifest\"\n        ]\n    },\n    \"text/calendar\": {\n        source: \"iana\",\n        extensions: [\n            \"ics\",\n            \"ifb\"\n        ]\n    },\n    \"text/css\": {\n        source: \"iana\",\n        charset: \"UTF-8\",\n        extensions: [\n            \"css\"\n        ]\n    },\n    \"text/csv\": {\n        source: \"iana\",\n        extensions: [\n            \"csv\"\n        ]\n    },\n    \"text/html\": {\n        source: \"iana\",\n        extensions: [\n            \"html\",\n            \"htm\",\n            \"shtml\"\n        ]\n    },\n    \"text/markdown\": {\n        source: \"iana\",\n        extensions: [\n            \"markdown\",\n            \"md\"\n        ]\n    },\n    \"text/mathml\": {\n        source: \"nginx\",\n        extensions: [\n            \"mml\"\n        ]\n    },\n    \"text/n3\": {\n        source: \"iana\",\n        charset: \"UTF-8\",\n        extensions: [\n            \"n3\"\n        ]\n    },\n    \"text/plain\": {\n        source: \"iana\",\n        extensions: [\n            \"txt\",\n            \"text\",\n            \"conf\",\n            \"def\",\n            \"list\",\n            \"log\",\n            \"in\",\n            \"ini\"\n        ]\n    },\n    \"text/prs.lines.tag\": {\n        source: \"iana\",\n        extensions: [\n            \"dsc\"\n        ]\n    },\n    \"text/richtext\": {\n        source: \"iana\",\n        extensions: [\n            \"rtx\"\n        ]\n    },\n    \"text/rtf\": {\n        source: \"iana\",\n        extensions: [\n            \"rtf\"\n        ]\n    },\n    \"text/sgml\": {\n        source: \"iana\",\n        extensions: [\n            \"sgml\",\n            \"sgm\"\n        ]\n    },\n    \"text/shex\": {\n        source: \"iana\",\n        extensions: [\n            \"shex\"\n        ]\n    },\n    \"text/spdx\": {\n        source: \"iana\",\n        extensions: [\n            \"spdx\"\n        ]\n    },\n    \"text/tab-separated-values\": {\n        source: \"iana\",\n        extensions: [\n            \"tsv\"\n        ]\n    },\n    \"text/troff\": {\n        source: \"iana\",\n        extensions: [\n            \"t\",\n            \"tr\",\n            \"roff\",\n            \"man\",\n            \"me\",\n            \"ms\"\n        ]\n    },\n    \"text/turtle\": {\n        source: \"iana\",\n        charset: \"UTF-8\",\n        extensions: [\n            \"ttl\"\n        ]\n    },\n    \"text/uri-list\": {\n        source: \"iana\",\n        extensions: [\n            \"uri\",\n            \"uris\",\n            \"urls\"\n        ]\n    },\n    \"text/vcard\": {\n        source: \"iana\",\n        extensions: [\n            \"vcard\"\n        ]\n    },\n    \"text/vnd.curl\": {\n        source: \"iana\",\n        extensions: [\n            \"curl\"\n        ]\n    },\n    \"text/vnd.curl.dcurl\": {\n        source: \"apache\",\n        extensions: [\n            \"dcurl\"\n        ]\n    },\n    \"text/vnd.curl.mcurl\": {\n        source: \"apache\",\n        extensions: [\n            \"mcurl\"\n        ]\n    },\n    \"text/vnd.curl.scurl\": {\n        source: \"apache\",\n        extensions: [\n            \"scurl\"\n        ]\n    },\n    \"text/vnd.dvb.subtitle\": {\n        source: \"iana\",\n        extensions: [\n            \"sub\"\n        ]\n    },\n    \"text/vnd.familysearch.gedcom\": {\n        source: \"iana\",\n        extensions: [\n            \"ged\"\n        ]\n    },\n    \"text/vnd.fly\": {\n        source: \"iana\",\n        extensions: [\n            \"fly\"\n        ]\n    },\n    \"text/vnd.fmi.flexstor\": {\n        source: \"iana\",\n        extensions: [\n            \"flx\"\n        ]\n    },\n    \"text/vnd.graphviz\": {\n        source: \"iana\",\n        extensions: [\n            \"gv\"\n        ]\n    },\n    \"text/vnd.in3d.3dml\": {\n        source: \"iana\",\n        extensions: [\n            \"3dml\"\n        ]\n    },\n    \"text/vnd.in3d.spot\": {\n        source: \"iana\",\n        extensions: [\n            \"spot\"\n        ]\n    },\n    \"text/vnd.sun.j2me.app-descriptor\": {\n        source: \"iana\",\n        charset: \"UTF-8\",\n        extensions: [\n            \"jad\"\n        ]\n    },\n    \"text/vnd.wap.wml\": {\n        source: \"iana\",\n        extensions: [\n            \"wml\"\n        ]\n    },\n    \"text/vnd.wap.wmlscript\": {\n        source: \"iana\",\n        extensions: [\n            \"wmls\"\n        ]\n    },\n    \"text/vtt\": {\n        source: \"iana\",\n        charset: \"UTF-8\",\n        extensions: [\n            \"vtt\"\n        ]\n    },\n    \"text/x-asm\": {\n        source: \"apache\",\n        extensions: [\n            \"s\",\n            \"asm\"\n        ]\n    },\n    \"text/x-c\": {\n        source: \"apache\",\n        extensions: [\n            \"c\",\n            \"cc\",\n            \"cxx\",\n            \"cpp\",\n            \"h\",\n            \"hh\",\n            \"dic\"\n        ]\n    },\n    \"text/x-component\": {\n        source: \"nginx\",\n        extensions: [\n            \"htc\"\n        ]\n    },\n    \"text/x-fortran\": {\n        source: \"apache\",\n        extensions: [\n            \"f\",\n            \"for\",\n            \"f77\",\n            \"f90\"\n        ]\n    },\n    \"text/x-java-source\": {\n        source: \"apache\",\n        extensions: [\n            \"java\"\n        ]\n    },\n    \"text/x-nfo\": {\n        source: \"apache\",\n        extensions: [\n            \"nfo\"\n        ]\n    },\n    \"text/x-opml\": {\n        source: \"apache\",\n        extensions: [\n            \"opml\"\n        ]\n    },\n    \"text/x-pascal\": {\n        source: \"apache\",\n        extensions: [\n            \"p\",\n            \"pas\"\n        ]\n    },\n    \"text/x-setext\": {\n        source: \"apache\",\n        extensions: [\n            \"etx\"\n        ]\n    },\n    \"text/x-sfv\": {\n        source: \"apache\",\n        extensions: [\n            \"sfv\"\n        ]\n    },\n    \"text/x-uuencode\": {\n        source: \"apache\",\n        extensions: [\n            \"uu\"\n        ]\n    },\n    \"text/x-vcalendar\": {\n        source: \"apache\",\n        extensions: [\n            \"vcs\"\n        ]\n    },\n    \"text/x-vcard\": {\n        source: \"apache\",\n        extensions: [\n            \"vcf\"\n        ]\n    },\n    \"text/xml\": {\n        source: \"iana\",\n        extensions: [\n            \"xml\"\n        ]\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@uploadthing/mime-types/text/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@uploadthing/mime-types/video/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/@uploadthing/mime-types/video/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   video: () => (/* binding */ video)\n/* harmony export */ });\nconst video = {\n    \"video/3gpp\": {\n        source: \"iana\",\n        extensions: [\n            \"3gp\",\n            \"3gpp\"\n        ]\n    },\n    \"video/3gpp2\": {\n        source: \"iana\",\n        extensions: [\n            \"3g2\"\n        ]\n    },\n    \"video/h261\": {\n        source: \"iana\",\n        extensions: [\n            \"h261\"\n        ]\n    },\n    \"video/h263\": {\n        source: \"iana\",\n        extensions: [\n            \"h263\"\n        ]\n    },\n    \"video/h264\": {\n        source: \"iana\",\n        extensions: [\n            \"h264\"\n        ]\n    },\n    \"video/iso.segment\": {\n        source: \"iana\",\n        extensions: [\n            \"m4s\"\n        ]\n    },\n    \"video/jpeg\": {\n        source: \"iana\",\n        extensions: [\n            \"jpgv\"\n        ]\n    },\n    \"video/jpm\": {\n        source: \"apache\",\n        extensions: [\n            \"jpm\",\n            \"jpgm\"\n        ]\n    },\n    \"video/mj2\": {\n        source: \"iana\",\n        extensions: [\n            \"mj2\",\n            \"mjp2\"\n        ]\n    },\n    \"video/mp2t\": {\n        source: \"iana\",\n        extensions: [\n            \"ts\"\n        ]\n    },\n    \"video/mp4\": {\n        source: \"iana\",\n        extensions: [\n            \"mp4\",\n            \"mp4v\",\n            \"mpg4\"\n        ]\n    },\n    \"video/mpeg\": {\n        source: \"iana\",\n        extensions: [\n            \"mpeg\",\n            \"mpg\",\n            \"mpe\",\n            \"m1v\",\n            \"m2v\"\n        ]\n    },\n    \"video/ogg\": {\n        source: \"iana\",\n        extensions: [\n            \"ogv\"\n        ]\n    },\n    \"video/quicktime\": {\n        source: \"iana\",\n        extensions: [\n            \"qt\",\n            \"mov\"\n        ]\n    },\n    \"video/vnd.dece.hd\": {\n        source: \"iana\",\n        extensions: [\n            \"uvh\",\n            \"uvvh\"\n        ]\n    },\n    \"video/vnd.dece.mobile\": {\n        source: \"iana\",\n        extensions: [\n            \"uvm\",\n            \"uvvm\"\n        ]\n    },\n    \"video/vnd.dece.pd\": {\n        source: \"iana\",\n        extensions: [\n            \"uvp\",\n            \"uvvp\"\n        ]\n    },\n    \"video/vnd.dece.sd\": {\n        source: \"iana\",\n        extensions: [\n            \"uvs\",\n            \"uvvs\"\n        ]\n    },\n    \"video/vnd.dece.video\": {\n        source: \"iana\",\n        extensions: [\n            \"uvv\",\n            \"uvvv\"\n        ]\n    },\n    \"video/vnd.dvb.file\": {\n        source: \"iana\",\n        extensions: [\n            \"dvb\"\n        ]\n    },\n    \"video/vnd.fvt\": {\n        source: \"iana\",\n        extensions: [\n            \"fvt\"\n        ]\n    },\n    \"video/vnd.mpegurl\": {\n        source: \"iana\",\n        extensions: [\n            \"mxu\",\n            \"m4u\"\n        ]\n    },\n    \"video/vnd.ms-playready.media.pyv\": {\n        source: \"iana\",\n        extensions: [\n            \"pyv\"\n        ]\n    },\n    \"video/vnd.uvvu.mp4\": {\n        source: \"iana\",\n        extensions: [\n            \"uvu\",\n            \"uvvu\"\n        ]\n    },\n    \"video/vnd.vivo\": {\n        source: \"iana\",\n        extensions: [\n            \"viv\"\n        ]\n    },\n    \"video/webm\": {\n        source: \"apache\",\n        extensions: [\n            \"webm\"\n        ]\n    },\n    \"video/x-f4v\": {\n        source: \"apache\",\n        extensions: [\n            \"f4v\"\n        ]\n    },\n    \"video/x-fli\": {\n        source: \"apache\",\n        extensions: [\n            \"fli\"\n        ]\n    },\n    \"video/x-flv\": {\n        source: \"apache\",\n        extensions: [\n            \"flv\"\n        ]\n    },\n    \"video/x-m4v\": {\n        source: \"apache\",\n        extensions: [\n            \"m4v\"\n        ]\n    },\n    \"video/x-matroska\": {\n        source: \"apache\",\n        extensions: [\n            \"mkv\",\n            \"mk3d\",\n            \"mks\"\n        ]\n    },\n    \"video/x-mng\": {\n        source: \"apache\",\n        extensions: [\n            \"mng\"\n        ]\n    },\n    \"video/x-ms-asf\": {\n        source: \"apache\",\n        extensions: [\n            \"asf\",\n            \"asx\"\n        ]\n    },\n    \"video/x-ms-vob\": {\n        source: \"apache\",\n        extensions: [\n            \"vob\"\n        ]\n    },\n    \"video/x-ms-wm\": {\n        source: \"apache\",\n        extensions: [\n            \"wm\"\n        ]\n    },\n    \"video/x-ms-wmv\": {\n        source: \"apache\",\n        extensions: [\n            \"wmv\"\n        ]\n    },\n    \"video/x-ms-wmx\": {\n        source: \"apache\",\n        extensions: [\n            \"wmx\"\n        ]\n    },\n    \"video/x-ms-wvx\": {\n        source: \"apache\",\n        extensions: [\n            \"wvx\"\n        ]\n    },\n    \"video/x-msvideo\": {\n        source: \"apache\",\n        extensions: [\n            \"avi\"\n        ]\n    },\n    \"video/x-sgi-movie\": {\n        source: \"apache\",\n        extensions: [\n            \"movie\"\n        ]\n    },\n    \"video/x-smv\": {\n        source: \"apache\",\n        extensions: [\n            \"smv\"\n        ]\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@uploadthing/mime-types/video/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@uploadthing/shared/dist/index.js":
/*!********************************************************!*\
  !*** ./node_modules/@uploadthing/shared/dist/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALLOWED_FILE_TYPES: () => (/* binding */ ALLOWED_FILE_TYPES),\n/* harmony export */   BadRequestError: () => (/* binding */ BadRequestError),\n/* harmony export */   FILESIZE_UNITS: () => (/* binding */ FILESIZE_UNITS),\n/* harmony export */   FetchContext: () => (/* binding */ FetchContext),\n/* harmony export */   FetchError: () => (/* binding */ FetchError),\n/* harmony export */   INTERNAL_DO_NOT_USE__fatalClientError: () => (/* binding */ INTERNAL_DO_NOT_USE__fatalClientError),\n/* harmony export */   INTERNAL_doFormatting: () => (/* binding */ INTERNAL_doFormatting),\n/* harmony export */   InvalidFileSizeError: () => (/* binding */ InvalidFileSizeError),\n/* harmony export */   InvalidFileTypeError: () => (/* binding */ InvalidFileTypeError),\n/* harmony export */   InvalidJsonError: () => (/* binding */ InvalidJsonError),\n/* harmony export */   InvalidRouteConfigError: () => (/* binding */ InvalidRouteConfigError),\n/* harmony export */   InvalidURLError: () => (/* binding */ InvalidURLError),\n/* harmony export */   RetryError: () => (/* binding */ RetryError),\n/* harmony export */   UnknownFileTypeError: () => (/* binding */ UnknownFileTypeError),\n/* harmony export */   UploadAbortedError: () => (/* binding */ UploadAbortedError),\n/* harmony export */   UploadPausedError: () => (/* binding */ UploadPausedError),\n/* harmony export */   UploadThingError: () => (/* binding */ UploadThingError),\n/* harmony export */   ValidACLs: () => (/* binding */ ValidACLs),\n/* harmony export */   ValidContentDispositions: () => (/* binding */ ValidContentDispositions),\n/* harmony export */   acceptPropAsAcceptAttr: () => (/* binding */ acceptPropAsAcceptAttr),\n/* harmony export */   allFilesAccepted: () => (/* binding */ allFilesAccepted),\n/* harmony export */   allowedContentTextLabelGenerator: () => (/* binding */ allowedContentTextLabelGenerator),\n/* harmony export */   bytesToFileSize: () => (/* binding */ bytesToFileSize),\n/* harmony export */   capitalizeStart: () => (/* binding */ capitalizeStart),\n/* harmony export */   contentFieldToContent: () => (/* binding */ contentFieldToContent),\n/* harmony export */   createIdentityProxy: () => (/* binding */ createIdentityProxy),\n/* harmony export */   defaultClassListMerger: () => (/* binding */ defaultClassListMerger),\n/* harmony export */   fetchEff: () => (/* binding */ fetchEff),\n/* harmony export */   fileSizeToBytes: () => (/* binding */ fileSizeToBytes),\n/* harmony export */   fillInputRouteConfig: () => (/* binding */ fillInputRouteConfig),\n/* harmony export */   filterDefinedObjectValues: () => (/* binding */ filterDefinedObjectValues),\n/* harmony export */   generateClientDropzoneAccept: () => (/* binding */ generateClientDropzoneAccept),\n/* harmony export */   generateKey: () => (/* binding */ generateKey),\n/* harmony export */   generateMimeTypes: () => (/* binding */ generateMimeTypes),\n/* harmony export */   generatePermittedFileTypes: () => (/* binding */ generatePermittedFileTypes),\n/* harmony export */   generateSignedURL: () => (/* binding */ generateSignedURL),\n/* harmony export */   getDefaultRouteConfigValues: () => (/* binding */ getDefaultRouteConfigValues),\n/* harmony export */   getDefaultSizeForType: () => (/* binding */ getDefaultSizeForType),\n/* harmony export */   getErrorTypeFromStatusCode: () => (/* binding */ getErrorTypeFromStatusCode),\n/* harmony export */   getFilesFromClipboardEvent: () => (/* binding */ getFilesFromClipboardEvent),\n/* harmony export */   getFullApiUrl: () => (/* binding */ getFullApiUrl),\n/* harmony export */   getRequestUrl: () => (/* binding */ getRequestUrl),\n/* harmony export */   getStatusCodeFromError: () => (/* binding */ getStatusCodeFromError),\n/* harmony export */   initialState: () => (/* binding */ initialState),\n/* harmony export */   isEnterOrSpace: () => (/* binding */ isEnterOrSpace),\n/* harmony export */   isEventWithFiles: () => (/* binding */ isEventWithFiles),\n/* harmony export */   isFileAccepted: () => (/* binding */ isFileAccepted),\n/* harmony export */   isIeOrEdge: () => (/* binding */ isIeOrEdge),\n/* harmony export */   isPropagationStopped: () => (/* binding */ isPropagationStopped),\n/* harmony export */   isRouteArray: () => (/* binding */ isRouteArray),\n/* harmony export */   isValidQuantity: () => (/* binding */ isValidQuantity),\n/* harmony export */   isValidSize: () => (/* binding */ isValidSize),\n/* harmony export */   matchFileType: () => (/* binding */ matchFileType),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   objectKeys: () => (/* binding */ objectKeys),\n/* harmony export */   parseResponseJson: () => (/* binding */ parseResponseJson),\n/* harmony export */   parseTimeToSeconds: () => (/* binding */ parseTimeToSeconds),\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   resolveMaybeUrlArg: () => (/* binding */ resolveMaybeUrlArg),\n/* harmony export */   roundProgress: () => (/* binding */ roundProgress),\n/* harmony export */   safeNumberReplacer: () => (/* binding */ safeNumberReplacer),\n/* harmony export */   safeParseJSON: () => (/* binding */ safeParseJSON),\n/* harmony export */   semverLite: () => (/* binding */ semverLite),\n/* harmony export */   signPayload: () => (/* binding */ signPayload),\n/* harmony export */   styleFieldToClassName: () => (/* binding */ styleFieldToClassName),\n/* harmony export */   styleFieldToCssObject: () => (/* binding */ styleFieldToCssObject),\n/* harmony export */   unwrap: () => (/* binding */ unwrap),\n/* harmony export */   verifyKey: () => (/* binding */ verifyKey),\n/* harmony export */   verifySignature: () => (/* binding */ verifySignature),\n/* harmony export */   warnIfInvalidPeerDependency: () => (/* binding */ warnIfInvalidPeerDependency)\n/* harmony export */ });\n/* harmony import */ var effect_Micro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! effect/Micro */ \"(rsc)/./node_modules/effect/dist/esm/Micro.js\");\n/* harmony import */ var _uploadthing_mime_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @uploadthing/mime-types */ \"(rsc)/./node_modules/@uploadthing/mime-types/dist/index.js\");\n/* harmony import */ var effect_Predicate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! effect/Predicate */ \"(rsc)/./node_modules/effect/dist/esm/Predicate.js\");\n/* harmony import */ var effect_Context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! effect/Context */ \"(rsc)/./node_modules/effect/dist/esm/Context.js\");\n/* harmony import */ var _uploadthing_mime_types_audio__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @uploadthing/mime-types/audio */ \"(rsc)/./node_modules/@uploadthing/mime-types/audio/index.js\");\n/* harmony import */ var _uploadthing_mime_types_image__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @uploadthing/mime-types/image */ \"(rsc)/./node_modules/@uploadthing/mime-types/image/index.js\");\n/* harmony import */ var _uploadthing_mime_types_text__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @uploadthing/mime-types/text */ \"(rsc)/./node_modules/@uploadthing/mime-types/text/index.js\");\n/* harmony import */ var _uploadthing_mime_types_video__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @uploadthing/mime-types/video */ \"(rsc)/./node_modules/@uploadthing/mime-types/video/index.js\");\n/* harmony import */ var effect_Encoding__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! effect/Encoding */ \"(rsc)/./node_modules/effect/dist/esm/Encoding.js\");\n/* harmony import */ var effect_Hash__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! effect/Hash */ \"(rsc)/./node_modules/effect/dist/esm/Hash.js\");\n/* harmony import */ var effect_Redacted__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! effect/Redacted */ \"(rsc)/./node_modules/effect/dist/esm/Redacted.js\");\n/* harmony import */ var sqids__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sqids */ \"(rsc)/./node_modules/sqids/esm/sqids.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ValidContentDispositions = [\n    \"inline\",\n    \"attachment\"\n];\nconst ValidACLs = [\n    \"public-read\",\n    \"private\"\n];\n\nclass InvalidRouteConfigError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"InvalidRouteConfig\") {\n    constructor(type, field){\n        const reason = field ? `Expected route config to have a ${field} for key ${type} but none was found.` : `Encountered an invalid route config during backfilling. ${type} was not found.`;\n        super({\n            reason\n        });\n    }\n}\nclass UnknownFileTypeError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"UnknownFileType\") {\n    constructor(fileName){\n        const reason = `Could not determine type for ${fileName}`;\n        super({\n            reason\n        });\n    }\n}\nclass InvalidFileTypeError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"InvalidFileType\") {\n    constructor(fileType, fileName){\n        const reason = `File type ${fileType} not allowed for ${fileName}`;\n        super({\n            reason\n        });\n    }\n}\nclass InvalidFileSizeError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"InvalidFileSize\") {\n    constructor(fileSize){\n        const reason = `Invalid file size: ${fileSize}`;\n        super({\n            reason\n        });\n    }\n}\nclass InvalidURLError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"InvalidURL\") {\n    constructor(attemptedUrl){\n        super({\n            reason: `Failed to parse '${attemptedUrl}' as a URL.`\n        });\n    }\n}\nclass RetryError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"RetryError\") {\n}\nclass FetchError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"FetchError\") {\n}\nclass InvalidJsonError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"InvalidJson\") {\n}\nclass BadRequestError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"BadRequestError\") {\n    getMessage() {\n        if (effect_Predicate__WEBPACK_IMPORTED_MODULE_2__.isRecord(this.json)) {\n            if (typeof this.json.message === \"string\") return this.json.message;\n        }\n        return this.message;\n    }\n}\nclass UploadPausedError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"UploadAborted\") {\n}\nclass UploadAbortedError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"UploadAborted\") {\n}\n\nfunction isRouteArray(routeConfig) {\n    return Array.isArray(routeConfig);\n}\nfunction getDefaultSizeForType(fileType) {\n    if (fileType === \"image\") return \"4MB\";\n    if (fileType === \"video\") return \"16MB\";\n    if (fileType === \"audio\") return \"8MB\";\n    if (fileType === \"blob\") return \"8MB\";\n    if (fileType === \"pdf\") return \"4MB\";\n    if (fileType === \"text\") return \"64KB\";\n    return \"4MB\";\n}\nfunction getDefaultRouteConfigValues(type) {\n    return {\n        maxFileSize: getDefaultSizeForType(type),\n        maxFileCount: 1,\n        minFileCount: 1,\n        contentDisposition: \"inline\"\n    };\n}\n/**\n * This function takes in the user's input and \"upscales\" it to a full config\n * Additionally, it replaces numbers with \"safe\" equivalents\n *\n * Example:\n * ```ts\n * [\"image\"] => { image: { maxFileSize: \"4MB\", limit: 1 } }\n * ```\n */ const fillInputRouteConfig = (routeConfig)=>{\n    // If array, apply defaults\n    if (isRouteArray(routeConfig)) {\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(routeConfig.reduce((acc, fileType)=>{\n            acc[fileType] = getDefaultRouteConfigValues(fileType);\n            return acc;\n        }, {}));\n    }\n    // Backfill defaults onto config\n    const newConfig = {};\n    for (const key of objectKeys(routeConfig)){\n        const value = routeConfig[key];\n        if (!value) return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new InvalidRouteConfigError(key));\n        newConfig[key] = {\n            ...getDefaultRouteConfigValues(key),\n            ...value\n        };\n    }\n    // we know that the config is valid, so we can stringify it and parse it back\n    // this allows us to replace numbers with \"safe\" equivalents\n    return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(JSON.parse(JSON.stringify(newConfig, safeNumberReplacer)));\n};\n/**\n * Match the file's type for a given allow list e.g. `image/png => image`\n * Prefers the file's type, then falls back to a extension-based lookup\n */ const matchFileType = (file, allowedTypes)=>{\n    // Type might be \"\" if the browser doesn't recognize the mime type\n    const mimeType = file.type || (0,_uploadthing_mime_types__WEBPACK_IMPORTED_MODULE_3__.lookup)(file.name);\n    if (!mimeType) {\n        if (allowedTypes.includes(\"blob\")) return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(\"blob\");\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new UnknownFileTypeError(file.name));\n    }\n    // If the user has specified a specific mime type, use that\n    if (allowedTypes.some((type)=>type.includes(\"/\"))) {\n        if (allowedTypes.includes(mimeType)) {\n            return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(mimeType);\n        }\n    }\n    // Otherwise, we have a \"magic\" type eg. \"image\" or \"video\"\n    const type = mimeType.toLowerCase() === \"application/pdf\" ? \"pdf\" : mimeType.split(\"/\")[0];\n    if (!allowedTypes.includes(type)) {\n        // Blob is a catch-all for any file type not explicitly supported\n        if (allowedTypes.includes(\"blob\")) {\n            return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(\"blob\");\n        } else {\n            return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new InvalidFileTypeError(type, file.name));\n        }\n    }\n    return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(type);\n};\nconst FILESIZE_UNITS = [\n    \"B\",\n    \"KB\",\n    \"MB\",\n    \"GB\",\n    \"TB\"\n];\nconst fileSizeToBytes = (fileSize)=>{\n    const regex = new RegExp(`^(\\\\d+)(\\\\.\\\\d+)?\\\\s*(${FILESIZE_UNITS.join(\"|\")})$`, \"i\");\n    // make sure the string is in the format of 123KB\n    const match = fileSize.match(regex);\n    if (!match?.[1] || !match[3]) {\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new InvalidFileSizeError(fileSize));\n    }\n    const sizeValue = parseFloat(match[1]);\n    const sizeUnit = match[3].toUpperCase();\n    const bytes = sizeValue * Math.pow(1024, FILESIZE_UNITS.indexOf(sizeUnit));\n    return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(Math.floor(bytes));\n};\nconst bytesToFileSize = (bytes)=>{\n    if (bytes === 0 || bytes === -1) {\n        return \"0B\";\n    }\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return `${(bytes / Math.pow(1024, i)).toFixed(2)}${FILESIZE_UNITS[i]}`;\n};\nasync function safeParseJSON(input) {\n    const text = await input.text();\n    try {\n        return JSON.parse(text);\n    } catch (err) {\n        // eslint-disable-next-line no-console\n        console.error(`Error parsing JSON, got '${text}'`, err);\n        return new Error(`Error parsing JSON, got '${text}'`);\n    }\n}\n/** typesafe Object.keys */ function objectKeys(obj) {\n    return Object.keys(obj);\n}\nfunction filterDefinedObjectValues(obj) {\n    return Object.fromEntries(Object.entries(obj).filter((pair)=>pair[1] != null));\n}\nfunction semverLite(required, toCheck) {\n    // Pull out numbers from strings like `6.0.0`, `^6.4`, `~6.4.0`\n    const semverRegex = /(\\d+)\\.?(\\d+)?\\.?(\\d+)?/;\n    const requiredMatch = semverRegex.exec(required);\n    if (!requiredMatch?.[0]) {\n        throw new Error(`Invalid semver requirement: ${required}`);\n    }\n    const toCheckMatch = semverRegex.exec(toCheck);\n    if (!toCheckMatch?.[0]) {\n        throw new Error(`Invalid semver to check: ${toCheck}`);\n    }\n    const [_1, rMajor, rMinor, rPatch] = requiredMatch;\n    const [_2, cMajor, cMinor, cPatch] = toCheckMatch;\n    if (required.startsWith(\"^\")) {\n        // Major must be equal, minor must be greater or equal\n        if (rMajor !== cMajor) return false;\n        if (rMinor && cMinor && rMinor > cMinor) return false;\n        return true;\n    }\n    if (required.startsWith(\"~\")) {\n        // Major must be equal, minor must be equal\n        if (rMajor !== cMajor) return false;\n        if (rMinor !== cMinor) return false;\n        return true;\n    }\n    // Exact match\n    return rMajor === cMajor && rMinor === cMinor && rPatch === cPatch;\n}\nfunction warnIfInvalidPeerDependency(pkg, required, toCheck) {\n    if (!semverLite(required, toCheck)) {\n        // eslint-disable-next-line no-console\n        console.warn(`!!!WARNING::: ${pkg} requires \"uploadthing@${required}\", but version \"${toCheck}\" is installed`);\n    }\n}\nconst getRequestUrl = (req)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        const host = req.headers.get(\"x-forwarded-host\") ?? req.headers.get(\"host\");\n        const proto = req.headers.get(\"x-forwarded-proto\") ?? \"https\";\n        const protocol = proto.endsWith(\":\") ? proto : `${proto}:`;\n        const url = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__[\"try\"]({\n            try: ()=>new URL(req.url, `${protocol}//${host}`),\n            catch: ()=>new InvalidURLError(req.url)\n        });\n        url.search = \"\";\n        return url;\n    });\nconst getFullApiUrl = (maybeUrl)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        const base = (()=>{\n            if (typeof window !== \"undefined\") return window.location.origin;\n            if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`;\n            return \"http://localhost:3000\";\n        })();\n        const url = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__[\"try\"]({\n            try: ()=>new URL(maybeUrl ?? \"/api/uploadthing\", base),\n            catch: ()=>new InvalidURLError(maybeUrl ?? \"/api/uploadthing\")\n        });\n        if (url.pathname === \"/\") {\n            url.pathname = \"/api/uploadthing\";\n        }\n        return url;\n    });\n/*\n * Returns a full URL to the dev's uploadthing endpoint\n * Can take either an origin, or a pathname, or a full URL\n * and will return the \"closest\" url matching the default\n * `<VERCEL_URL || localhost>/api/uploadthing`\n */ const resolveMaybeUrlArg = (maybeUrl)=>{\n    return maybeUrl instanceof URL ? maybeUrl : effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runSync(getFullApiUrl(maybeUrl));\n};\nfunction parseTimeToSeconds(time) {\n    if (typeof time === \"number\") return time;\n    const match = time.split(/(\\d+)/).filter(Boolean);\n    const num = Number(match[0]);\n    const unit = (match[1] ?? \"s\").trim().slice(0, 1);\n    const multiplier = {\n        s: 1,\n        m: 60,\n        h: 3600,\n        d: 86400\n    }[unit];\n    return num * multiplier;\n}\n/**\n * Replacer for JSON.stringify that will replace numbers that cannot be\n * serialized to JSON with \"reasonable equivalents\".\n *\n * Infinity and -Infinity are replaced by MAX_SAFE_INTEGER and MIN_SAFE_INTEGER\n * NaN is replaced by 0\n *\n */ const safeNumberReplacer = (_, value)=>{\n    if (typeof value !== \"number\") return value;\n    if (Number.isSafeInteger(value) || value <= Number.MAX_SAFE_INTEGER && value >= Number.MIN_SAFE_INTEGER) {\n        return value;\n    }\n    if (value === Infinity) return Number.MAX_SAFE_INTEGER;\n    if (value === -Infinity) return Number.MIN_SAFE_INTEGER;\n    if (Number.isNaN(value)) return 0;\n};\nfunction noop() {\n// noop\n}\nfunction createIdentityProxy() {\n    return new Proxy(noop, {\n        get: (_, prop)=>prop\n    });\n}\nfunction unwrap(x, ...args) {\n    return typeof x === \"function\" ? x(...args) : x;\n}\n\nconst ALLOWED_FILE_TYPES = [\n    \"image\",\n    \"video\",\n    \"audio\",\n    \"pdf\",\n    \"text\",\n    \"blob\"\n];\n\nconst ERROR_CODES = {\n    // Generic\n    BAD_REQUEST: 400,\n    NOT_FOUND: 404,\n    FORBIDDEN: 403,\n    INTERNAL_SERVER_ERROR: 500,\n    INTERNAL_CLIENT_ERROR: 500,\n    // S3 specific\n    TOO_LARGE: 413,\n    TOO_SMALL: 400,\n    TOO_MANY_FILES: 400,\n    KEY_TOO_LONG: 400,\n    // UploadThing specific\n    URL_GENERATION_FAILED: 500,\n    UPLOAD_FAILED: 500,\n    MISSING_ENV: 500,\n    INVALID_SERVER_CONFIG: 500,\n    FILE_LIMIT_EXCEEDED: 500\n};\nfunction messageFromUnknown(cause, fallback) {\n    if (typeof cause === \"string\") {\n        return cause;\n    }\n    if (cause instanceof Error) {\n        return cause.message;\n    }\n    if (cause && typeof cause === \"object\" && \"message\" in cause && typeof cause.message === \"string\") {\n        return cause.message;\n    }\n    return fallback ?? \"An unknown error occurred\";\n}\nclass UploadThingError extends effect_Micro__WEBPACK_IMPORTED_MODULE_1__.Error {\n    constructor(initOpts){\n        const opts = typeof initOpts === \"string\" ? {\n            code: \"INTERNAL_SERVER_ERROR\",\n            message: initOpts\n        } : initOpts;\n        const message = opts.message ?? messageFromUnknown(opts.cause, opts.code);\n        super({\n            message\n        }), this._tag = \"UploadThingError\", this.name = \"UploadThingError\";\n        this.code = opts.code;\n        this.data = opts.data;\n        if (opts.cause instanceof Error) {\n            this.cause = opts.cause;\n        } else if (effect_Predicate__WEBPACK_IMPORTED_MODULE_2__.isRecord(opts.cause) && effect_Predicate__WEBPACK_IMPORTED_MODULE_2__.isNumber(opts.cause.status) && effect_Predicate__WEBPACK_IMPORTED_MODULE_2__.isString(opts.cause.statusText)) {\n            this.cause = new Error(`Response ${opts.cause.status} ${opts.cause.statusText}`);\n        } else if (effect_Predicate__WEBPACK_IMPORTED_MODULE_2__.isString(opts.cause)) {\n            this.cause = new Error(opts.cause);\n        } else {\n            this.cause = opts.cause;\n        }\n    }\n    static toObject(error) {\n        return {\n            code: error.code,\n            message: error.message,\n            data: error.data\n        };\n    }\n    static serialize(error) {\n        return JSON.stringify(UploadThingError.toObject(error));\n    }\n}\nfunction getErrorTypeFromStatusCode(statusCode) {\n    for (const [code, status] of Object.entries(ERROR_CODES)){\n        if (status === statusCode) {\n            return code;\n        }\n    }\n    return \"INTERNAL_SERVER_ERROR\";\n}\nfunction getStatusCodeFromError(error) {\n    return ERROR_CODES[error.code];\n}\nconst INTERNAL_DO_NOT_USE__fatalClientError = (e)=>new UploadThingError({\n        code: \"INTERNAL_CLIENT_ERROR\",\n        message: \"Something went wrong. Please report this to UploadThing.\",\n        cause: e\n    });\n\nclass FetchContext extends /** #__PURE__ */ effect_Context__WEBPACK_IMPORTED_MODULE_4__.Tag(\"uploadthing/Fetch\")() {\n}\n// Temporary Effect wrappers below.\n// Only for use in the browser.\n// On the server, use `@effect/platform.HttpClient` instead.\nconst fetchEff = (input, init)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.flatMap(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.service(FetchContext), (fetch)=>{\n        const headers = new Headers(init?.headers ?? []);\n        const reqInfo = {\n            url: input.toString(),\n            method: init?.method,\n            body: init?.body,\n            headers: Object.fromEntries(headers)\n        };\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tryPromise({\n            try: (signal)=>fetch(input, {\n                    ...init,\n                    headers,\n                    signal\n                }),\n            catch: (error)=>new FetchError({\n                    error: error instanceof Error ? {\n                        ...error,\n                        name: error.name,\n                        message: error.message,\n                        stack: error.stack\n                    } : error,\n                    input: reqInfo\n                })\n        }).pipe(// eslint-disable-next-line no-console\n        effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tapError((e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.sync(()=>console.error(e.input))), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map((res)=>Object.assign(res, {\n                requestUrl: reqInfo.url\n            })), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"fetch\"));\n    });\nconst parseResponseJson = (res)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tryPromise({\n        try: async ()=>{\n            const json = await res.json();\n            return {\n                json,\n                ok: res.ok,\n                status: res.status\n            };\n        },\n        catch: (error)=>new InvalidJsonError({\n                error,\n                input: res.requestUrl\n            })\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.filterOrFail(({ ok })=>ok, ({ json, status })=>new BadRequestError({\n            status,\n            message: `Request to ${res.requestUrl} failed with status ${status}`,\n            json\n        })), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map(({ json })=>json), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"parseJson\"));\n\nconst roundProgress = (progress, granularity)=>{\n    if (granularity === \"all\") return progress;\n    if (granularity === \"fine\") return Math.round(progress);\n    return Math.floor(progress / 10) * 10;\n};\nconst generateMimeTypes = (typesOrRouteConfig)=>{\n    const fileTypes = Array.isArray(typesOrRouteConfig) ? typesOrRouteConfig : objectKeys(typesOrRouteConfig);\n    if (fileTypes.includes(\"blob\")) return [];\n    return fileTypes.map((type)=>{\n        if (type === \"pdf\") return \"application/pdf\";\n        if (type.includes(\"/\")) return type;\n        // Add wildcard to support all subtypes, e.g. image => \"image/*\"\n        // But some browsers/OSes don't support it, so we'll also dump all the mime types\n        // we know that starts with the type, e.g. image => \"image/png, image/jpeg, ...\"\n        if (type === \"audio\") return [\n            \"audio/*\",\n            ...objectKeys(_uploadthing_mime_types_audio__WEBPACK_IMPORTED_MODULE_5__.audio)\n        ].join(\", \");\n        if (type === \"image\") return [\n            \"image/*\",\n            ...objectKeys(_uploadthing_mime_types_image__WEBPACK_IMPORTED_MODULE_6__.image)\n        ].join(\", \");\n        if (type === \"text\") return [\n            \"text/*\",\n            ...objectKeys(_uploadthing_mime_types_text__WEBPACK_IMPORTED_MODULE_7__.text)\n        ].join(\", \");\n        if (type === \"video\") return [\n            \"video/*\",\n            ...objectKeys(_uploadthing_mime_types_video__WEBPACK_IMPORTED_MODULE_8__.video)\n        ].join(\", \");\n        return `${type}/*`;\n    });\n};\nconst generateClientDropzoneAccept = (fileTypes)=>{\n    const mimeTypes = generateMimeTypes(fileTypes);\n    return Object.fromEntries(mimeTypes.map((type)=>[\n            type,\n            []\n        ]));\n};\nfunction getFilesFromClipboardEvent(event) {\n    const dataTransferItems = event.clipboardData?.items;\n    if (!dataTransferItems) return;\n    const files = Array.from(dataTransferItems).reduce((acc, curr)=>{\n        const f = curr.getAsFile();\n        return f ? [\n            ...acc,\n            f\n        ] : acc;\n    }, []);\n    return files;\n}\n/**\n * Shared helpers for our premade components that's reusable by multiple frameworks\n */ const generatePermittedFileTypes = (config)=>{\n    const fileTypes = config ? objectKeys(config) : [];\n    const maxFileCount = config ? Object.values(config).map((v)=>v.maxFileCount) : [];\n    return {\n        fileTypes,\n        multiple: maxFileCount.some((v)=>v && v > 1)\n    };\n};\nconst capitalizeStart = (str)=>{\n    return str.charAt(0).toUpperCase() + str.slice(1);\n};\nconst INTERNAL_doFormatting = (config)=>{\n    if (!config) return \"\";\n    const allowedTypes = objectKeys(config);\n    const formattedTypes = allowedTypes.map((f)=>f === \"blob\" ? \"file\" : f);\n    // Format multi-type uploader label as \"Supports videos, images and files\";\n    if (formattedTypes.length > 1) {\n        const lastType = formattedTypes.pop();\n        return `${formattedTypes.join(\"s, \")} and ${lastType}s`;\n    }\n    // Single type uploader label\n    const key = allowedTypes[0];\n    const formattedKey = formattedTypes[0];\n    if (!key || !formattedKey) return \"\";\n    const { maxFileSize, maxFileCount, minFileCount } = config[key];\n    if (maxFileCount && maxFileCount > 1) {\n        if (minFileCount > 1) {\n            return `${minFileCount} - ${maxFileCount} ${formattedKey}s up to ${maxFileSize}`;\n        } else {\n            return `${formattedKey}s up to ${maxFileSize}, max ${maxFileCount}`;\n        }\n    } else {\n        return `${formattedKey} (${maxFileSize})`;\n    }\n};\nconst allowedContentTextLabelGenerator = (config)=>{\n    return capitalizeStart(INTERNAL_doFormatting(config));\n};\nconst styleFieldToClassName = (styleField, args)=>{\n    if (typeof styleField === \"string\") return styleField;\n    if (typeof styleField === \"function\") {\n        const result = styleField(args);\n        if (typeof result === \"string\") return result;\n    }\n    return \"\";\n};\nconst styleFieldToCssObject = (styleField, args)=>{\n    if (typeof styleField === \"object\") return styleField;\n    if (typeof styleField === \"function\") {\n        const result = styleField(args);\n        if (typeof result === \"object\") return result;\n    }\n    return {};\n};\nconst contentFieldToContent = (contentField, arg)=>{\n    if (!contentField) return null;\n    if (typeof contentField !== \"function\") return contentField;\n    if (typeof contentField === \"function\") {\n        const result = contentField(arg);\n        return result;\n    }\n};\nconst defaultClassListMerger = (...classes)=>{\n    return classes.filter(Boolean).join(\" \");\n};\n\nconst signaturePrefix = \"hmac-sha256=\";\nconst algorithm = {\n    name: \"HMAC\",\n    hash: \"SHA-256\"\n};\nconst encoder = new TextEncoder();\nfunction shuffle(str, seed) {\n    const chars = str.split(\"\");\n    const seedNum = effect_Hash__WEBPACK_IMPORTED_MODULE_9__.string(seed);\n    let temp;\n    let j;\n    for(let i = 0; i < chars.length; i++){\n        j = (seedNum % (i + 1) + i) % chars.length;\n        temp = chars[i];\n        chars[i] = chars[j];\n        chars[j] = temp;\n    }\n    return chars.join(\"\");\n}\nconst signPayload = (payload, secret)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        const signingKey = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tryPromise({\n            try: ()=>crypto.subtle.importKey(\"raw\", encoder.encode(effect_Redacted__WEBPACK_IMPORTED_MODULE_10__.value(secret)), algorithm, false, [\n                    \"sign\"\n                ]),\n            catch: (e)=>new UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"Invalid signing secret\",\n                    cause: e\n                })\n        });\n        const signature = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tryPromise({\n            try: ()=>crypto.subtle.sign(algorithm, signingKey, encoder.encode(payload)),\n            catch: (e)=>new UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    cause: e\n                })\n        }), (arrayBuffer)=>effect_Encoding__WEBPACK_IMPORTED_MODULE_11__.encodeHex(new Uint8Array(arrayBuffer)));\n        return `${signaturePrefix}${signature}`;\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"signPayload\"));\nconst verifySignature = (payload, signature, secret)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        const sig = signature?.slice(signaturePrefix.length);\n        if (!sig) return false;\n        const secretBytes = encoder.encode(effect_Redacted__WEBPACK_IMPORTED_MODULE_10__.value(secret));\n        const signingKey = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.promise(()=>crypto.subtle.importKey(\"raw\", secretBytes, algorithm, false, [\n                \"verify\"\n            ]));\n        const sigBytes = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fromEither(effect_Encoding__WEBPACK_IMPORTED_MODULE_11__.decodeHex(sig));\n        const payloadBytes = encoder.encode(payload);\n        return yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.promise(()=>crypto.subtle.verify(algorithm, signingKey, sigBytes, payloadBytes));\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"verifySignature\"), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.orElseSucceed(()=>false));\nconst generateKey = (file, appId, getHashParts)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.sync(()=>{\n        // Get the parts of which we should hash to constuct the key\n        // This allows the user to customize the hashing algorithm\n        // If they for example want to generate the same key for the\n        // same file whenever it was uploaded\n        const hashParts = JSON.stringify(getHashParts?.(file) ?? [\n            file.name,\n            file.size,\n            file.type,\n            file.lastModified,\n            Date.now()\n        ]);\n        // Hash and Encode the parts and appId as sqids\n        const alphabet = shuffle(sqids__WEBPACK_IMPORTED_MODULE_0__.defaultOptions.alphabet, appId);\n        const encodedFileSeed = new sqids__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            alphabet,\n            minLength: 36\n        }).encode([\n            Math.abs(effect_Hash__WEBPACK_IMPORTED_MODULE_9__.string(hashParts))\n        ]);\n        const encodedAppId = new sqids__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            alphabet,\n            minLength: 12\n        }).encode([\n            Math.abs(effect_Hash__WEBPACK_IMPORTED_MODULE_9__.string(appId))\n        ]);\n        // Concatenate them\n        return encodedAppId + encodedFileSeed;\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"generateKey\"));\n// Verify that the key was generated with the same appId\nconst verifyKey = (key, appId)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.sync(()=>{\n        const alphabet = shuffle(sqids__WEBPACK_IMPORTED_MODULE_0__.defaultOptions.alphabet, appId);\n        const expectedPrefix = new sqids__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            alphabet,\n            minLength: 12\n        }).encode([\n            Math.abs(effect_Hash__WEBPACK_IMPORTED_MODULE_9__.string(appId))\n        ]);\n        return key.startsWith(expectedPrefix);\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"verifyKey\"), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.orElseSucceed(()=>false));\nconst generateSignedURL = (url, secretKey, opts)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        const parsedURL = new URL(url);\n        const ttl = opts.ttlInSeconds ? parseTimeToSeconds(opts.ttlInSeconds) : 60 * 60;\n        const expirationTime = Date.now() + ttl * 1000;\n        parsedURL.searchParams.append(\"expires\", expirationTime.toString());\n        if (opts.data) {\n            Object.entries(opts.data).forEach(([key, value])=>{\n                if (value == null) return;\n                const encoded = encodeURIComponent(value);\n                parsedURL.searchParams.append(key, encoded);\n            });\n        }\n        const signature = yield* signPayload(parsedURL.toString(), secretKey);\n        parsedURL.searchParams.append(\"signature\", signature);\n        return parsedURL.href;\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"generateSignedURL\"));\n\n/**\n * Copyright (c) (MIT License) 2015 Andrey Okonetchnikov\n * https://github.com/react-dropzone/attr-accept/blob/master/src/index.js\n */ function accepts(file, acceptedFiles) {\n    if (acceptedFiles) {\n        const acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(\",\");\n        const fileName = file.name;\n        const mimeType = file.type.toLowerCase();\n        const baseMimeType = mimeType.replace(/\\/.*$/, \"\");\n        return acceptedFilesArray.some((type)=>{\n            const validType = type.trim().toLowerCase();\n            if (validType.startsWith(\".\")) {\n                return fileName.toLowerCase().endsWith(validType);\n            } else if (validType.endsWith(\"/*\")) {\n                // This is something like a image/* mime type\n                return baseMimeType === validType.replace(/\\/.*$/, \"\");\n            }\n            return mimeType === validType;\n        });\n    }\n    return true;\n}\nconst isPropagationStopped = (event)=>{\n    if (typeof event.isPropagationStopped === \"function\") {\n        return event.isPropagationStopped();\n    }\n    if (typeof event.cancelBubble !== \"undefined\") {\n        return event.cancelBubble;\n    }\n    return false;\n};\n// Firefox versions prior to 53 return a bogus MIME type for every file drag, so dragovers with\n// that MIME type will always be accepted\nfunction isFileAccepted(file, accept) {\n    return file.type === \"application/x-moz-file\" || accepts(file, accept);\n}\nfunction isEnterOrSpace(event) {\n    return \"key\" in event && (event.key === \" \" || event.key === \"Enter\") || \"keyCode\" in event && (event.keyCode === 32 || event.keyCode === 13);\n}\nconst isDefined = (v)=>v != null;\nfunction isValidSize(file, minSize, maxSize) {\n    if (!isDefined(file.size)) return true;\n    if (isDefined(minSize) && isDefined(maxSize)) {\n        return file.size >= minSize && file.size <= maxSize;\n    }\n    if (isDefined(minSize) && file.size < minSize) return false;\n    if (isDefined(maxSize) && file.size > maxSize) return false;\n    return true;\n}\nfunction isValidQuantity(files, multiple, maxFiles) {\n    if (!multiple && files.length > 1) return false;\n    if (multiple && maxFiles >= 1 && files.length > maxFiles) return false;\n    return true;\n}\nfunction allFilesAccepted({ files, accept, minSize, maxSize, multiple, maxFiles }) {\n    if (!isValidQuantity(files, multiple, maxFiles)) return false;\n    return files.every((file)=>isFileAccepted(file, accept) && isValidSize(file, minSize, maxSize));\n}\nfunction isEventWithFiles(event) {\n    if (!(\"dataTransfer\" in event && event.dataTransfer !== null)) {\n        return !!event.target && \"files\" in event.target && !!event.target.files;\n    }\n    // https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/types\n    // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/Recommended_drag_types#file\n    return Array.prototype.some.call(// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    event.dataTransfer?.types, (type)=>type === \"Files\" || type === \"application/x-moz-file\");\n}\nfunction isIeOrEdge(ua = window.navigator.userAgent) {\n    return ua.includes(\"MSIE \") || ua.includes(\"Trident/\") || ua.includes(\"Edge/\");\n}\nfunction isMIMEType(v) {\n    return v === \"audio/*\" || v === \"video/*\" || v === \"image/*\" || v === \"text/*\" || /\\w+\\/[-+.\\w]+/g.test(v);\n}\nfunction isExt(v) {\n    return /^.*\\.[\\w]+$/.test(v);\n}\n/**\n * Convert the `{accept}` dropzone prop to an array of MIME types/extensions.\n */ function acceptPropAsAcceptAttr(accept) {\n    if (isDefined(accept)) {\n        return Object.entries(accept).reduce((a, [mimeType, ext])=>[\n                ...a,\n                mimeType,\n                ...ext\n            ], [])// Silently discard invalid entries as pickerOptionsFromAccept warns about these\n        .filter((v)=>isMIMEType(v) || isExt(v)).join(\",\");\n    }\n    return undefined;\n}\nconst initialState = {\n    isFocused: false,\n    isFileDialogActive: false,\n    isDragActive: false,\n    isDragAccept: false,\n    isDragReject: false,\n    acceptedFiles: []\n};\nfunction reducer(state, action) {\n    switch(action.type){\n        case \"focus\":\n            return {\n                ...state,\n                isFocused: true\n            };\n        case \"blur\":\n            return {\n                ...state,\n                isFocused: false\n            };\n        case \"openDialog\":\n            return {\n                ...initialState,\n                isFileDialogActive: true\n            };\n        case \"closeDialog\":\n            return {\n                ...state,\n                isFileDialogActive: false\n            };\n        case \"setDraggedFiles\":\n            return {\n                ...state,\n                ...action.payload\n            };\n        case \"setFiles\":\n            return {\n                ...state,\n                ...action.payload\n            };\n        case \"reset\":\n            return initialState;\n        default:\n            return state;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@uploadthing/shared/dist/index.js\n");

/***/ })

};
;