"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/multipasta";
exports.ids = ["vendor-chunks/multipasta"];
exports.modules = {

/***/ "(rsc)/./node_modules/multipasta/dist/esm/index.js":
/*!***************************************************!*\
  !*** ./node_modules/multipasta/dist/esm/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeField: () => (/* binding */ decodeField),\n/* harmony export */   defaultIsFile: () => (/* binding */ defaultIsFile),\n/* harmony export */   make: () => (/* binding */ make)\n/* harmony export */ });\n/* harmony import */ var _internal_multipart_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./internal/multipart.js */ \"(rsc)/./node_modules/multipasta/dist/esm/internal/multipart.js\");\n\nconst make = _internal_multipart_js__WEBPACK_IMPORTED_MODULE_0__.make;\nconst defaultIsFile = _internal_multipart_js__WEBPACK_IMPORTED_MODULE_0__.defaultIsFile;\nconst decodeField = _internal_multipart_js__WEBPACK_IMPORTED_MODULE_0__.decodeField;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbXVsdGlwYXN0YS9kaXN0L2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQW9EO0FBQzdDLGFBQWEsd0RBQWE7QUFDMUIsc0JBQXNCLGlFQUFzQjtBQUM1QyxvQkFBb0IsK0RBQW9CO0FBQy9DIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFobWVkXFxEZXNrdG9wXFxjb2RlXFxib290XFxkYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcbXVsdGlwYXN0YVxcZGlzdFxcZXNtXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBpbnRlcm5hbCBmcm9tIFwiLi9pbnRlcm5hbC9tdWx0aXBhcnQuanNcIjtcbmV4cG9ydCBjb25zdCBtYWtlID0gaW50ZXJuYWwubWFrZTtcbmV4cG9ydCBjb25zdCBkZWZhdWx0SXNGaWxlID0gaW50ZXJuYWwuZGVmYXVsdElzRmlsZTtcbmV4cG9ydCBjb25zdCBkZWNvZGVGaWVsZCA9IGludGVybmFsLmRlY29kZUZpZWxkO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/multipasta/dist/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/multipasta/dist/esm/internal/contentType.js":
/*!******************************************************************!*\
  !*** ./node_modules/multipasta/dist/esm/internal/contentType.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n// taken from https://github.com/fastify/fast-content-type-parse\n// under the MIT license\n/**\n * RegExp to match *( \";\" parameter ) in RFC 7231 sec *******\n *\n * parameter     = token \"=\" ( token / quoted-string )\n * token         = 1*tchar\n * tchar         = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" / \"*\"\n *               / \"+\" / \"-\" / \".\" / \"^\" / \"_\" / \"`\" / \"|\" / \"~\"\n *               / DIGIT / ALPHA\n *               ; any VCHAR, except delimiters\n * quoted-string = DQUOTE *( qdtext / quoted-pair ) DQUOTE\n * qdtext        = HTAB / SP / %x21 / %x23-5B / %x5D-7E / obs-text\n * obs-text      = %x80-FF\n * quoted-pair   = \"\\\" ( HTAB / SP / VCHAR / obs-text )\n */\nconst paramRE = /; *([!#$%&'*+.^\\w`|~-]+)=(\"(?:[\\v\\u0020\\u0021\\u0023-\\u005b\\u005d-\\u007e\\u0080-\\u00ff]|\\\\[\\v\\u0020-\\u00ff])*\"|[!#$%&'*+.^\\w`|~-]+) */gu;\n/**\n * RegExp to match quoted-pair in RFC 7230 sec 3.2.6\n *\n * quoted-pair = \"\\\" ( HTAB / SP / VCHAR / obs-text )\n * obs-text    = %x80-FF\n */\nconst quotedPairRE = /\\\\([\\v\\u0020-\\u00ff])/gu;\n/**\n * RegExp to match type in RFC 7231 sec *******\n *\n * media-type = type \"/\" subtype\n * type       = token\n * subtype    = token\n */\nconst mediaTypeRE = /^[!#$%&'*+.^\\w|~-]+\\/[!#$%&'*+.^\\w|~-]+$/u;\nconst mediaTypeRENoSlash = /^[!#$%&'*+.^\\w|~-]+$/u;\n// default ContentType to prevent repeated object creation\nconst defaultContentType = {\n  value: \"\",\n  parameters: /*#__PURE__*/Object.create(null)\n};\nfunction parse(header, withoutSlash = false) {\n  if (typeof header !== \"string\") {\n    return defaultContentType;\n  }\n  let index = header.indexOf(\";\");\n  const type = index !== -1 ? header.slice(0, index).trim() : header.trim();\n  const mediaRE = withoutSlash ? mediaTypeRENoSlash : mediaTypeRE;\n  if (mediaRE.test(type) === false) {\n    return defaultContentType;\n  }\n  const result = {\n    value: type.toLowerCase(),\n    parameters: Object.create(null)\n  };\n  // parse parameters\n  if (index === -1) {\n    return result;\n  }\n  let key;\n  let match;\n  let value;\n  paramRE.lastIndex = index;\n  while (match = paramRE.exec(header)) {\n    if (match.index !== index) {\n      return defaultContentType;\n    }\n    index += match[0].length;\n    key = match[1].toLowerCase();\n    value = match[2];\n    if (value[0] === '\"') {\n      // remove quotes and escapes\n      value = value.slice(1, value.length - 1);\n      !withoutSlash && quotedPairRE.test(value) && (value = value.replace(quotedPairRE, \"$1\"));\n    }\n    result.parameters[key] = value;\n  }\n  if (index !== header.length) {\n    return defaultContentType;\n  }\n  return result;\n}\n//# sourceMappingURL=contentType.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/multipasta/dist/esm/internal/contentType.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/multipasta/dist/esm/internal/headers.js":
/*!**************************************************************!*\
  !*** ./node_modules/multipasta/dist/esm/internal/headers.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   make: () => (/* binding */ make)\n/* harmony export */ });\nconst constMaxPairs = 100;\nconst constMaxSize = 16 * 1024;\nvar State;\n(function (State) {\n  State[State[\"key\"] = 0] = \"key\";\n  State[State[\"whitespace\"] = 1] = \"whitespace\";\n  State[State[\"value\"] = 2] = \"value\";\n})(State || (State = {}));\nconst constContinue = {\n  _tag: \"Continue\"\n};\nconst constNameChars = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1];\nconst constValueChars = [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1];\nfunction make() {\n  const decoder = new TextDecoder();\n  const state = {\n    state: State.key,\n    headers: Object.create(null),\n    key: \"\",\n    value: undefined,\n    crlf: 0,\n    previousChunk: undefined,\n    pairs: 0,\n    size: 0\n  };\n  function reset(value) {\n    state.state = State.key;\n    state.headers = Object.create(null);\n    state.key = \"\";\n    state.value = undefined;\n    state.crlf = 0;\n    state.previousChunk = undefined;\n    state.pairs = 0;\n    state.size = 0;\n    return value;\n  }\n  function concatUint8Array(a, b) {\n    const newUint8Array = new Uint8Array(a.length + b.length);\n    newUint8Array.set(a);\n    newUint8Array.set(b, a.length);\n    return newUint8Array;\n  }\n  function error(reason) {\n    return reset({\n      _tag: \"Failure\",\n      reason,\n      headers: state.headers\n    });\n  }\n  return function write(chunk, start) {\n    let endOffset = 0;\n    let previousCursor;\n    if (state.previousChunk !== undefined) {\n      endOffset = state.previousChunk.length;\n      previousCursor = endOffset;\n      const newChunk = new Uint8Array(chunk.length + endOffset);\n      newChunk.set(state.previousChunk);\n      newChunk.set(chunk, endOffset);\n      state.previousChunk = undefined;\n      chunk = newChunk;\n    }\n    const end = chunk.length;\n    outer: while (start < end) {\n      if (state.state === State.key) {\n        let i = start;\n        for (; i < end; i++) {\n          if (state.size++ > constMaxSize) {\n            return error(\"HeaderTooLarge\");\n          }\n          if (chunk[i] === 58) {\n            state.key += decoder.decode(chunk.subarray(start, i)).toLowerCase();\n            if (state.key.length === 0) {\n              return error(\"InvalidHeaderName\");\n            }\n            if (chunk[i + 1] === 32 && chunk[i + 2] !== 32 && chunk[i + 2] !== 9) {\n              start = i + 2;\n              state.state = State.value;\n              state.size++;\n            } else if (chunk[i + 1] !== 32 && chunk[i + 1] !== 9) {\n              start = i + 1;\n              state.state = State.value;\n            } else {\n              start = i + 1;\n              state.state = State.whitespace;\n            }\n            break;\n          } else if (constNameChars[chunk[i]] !== 1) {\n            return error(\"InvalidHeaderName\");\n          }\n        }\n        if (i === end) {\n          state.key += decoder.decode(chunk.subarray(start, end)).toLowerCase();\n          return constContinue;\n        }\n      }\n      if (state.state === State.whitespace) {\n        for (; start < end; start++) {\n          if (state.size++ > constMaxSize) {\n            return error(\"HeaderTooLarge\");\n          }\n          if (chunk[start] !== 32 && chunk[start] !== 9) {\n            state.state = State.value;\n            break;\n          }\n        }\n        if (start === end) {\n          return constContinue;\n        }\n      }\n      if (state.state === State.value) {\n        let i = start;\n        if (previousCursor !== undefined) {\n          i = previousCursor;\n          previousCursor = undefined;\n        }\n        for (; i < end; i++) {\n          if (state.size++ > constMaxSize) {\n            return error(\"HeaderTooLarge\");\n          }\n          if (chunk[i] === 13 || state.crlf > 0) {\n            let byte = chunk[i];\n            if (byte === 13 && state.crlf === 0) {\n              state.crlf = 1;\n              i++;\n              state.size++;\n              byte = chunk[i];\n            }\n            if (byte === 10 && state.crlf === 1) {\n              state.crlf = 2;\n              i++;\n              state.size++;\n              byte = chunk[i];\n            }\n            if (byte === 13 && state.crlf === 2) {\n              state.crlf = 3;\n              i++;\n              state.size++;\n              byte = chunk[i];\n            }\n            if (byte === 10 && state.crlf === 3) {\n              state.crlf = 4;\n              i++;\n              state.size++;\n            }\n            if (state.crlf < 4 && i >= end) {\n              state.previousChunk = chunk.subarray(start);\n              return constContinue;\n            } else if (state.crlf >= 2) {\n              state.value = state.value === undefined ? chunk.subarray(start, i - state.crlf) : concatUint8Array(state.value, chunk.subarray(start, i - state.crlf));\n              const value = decoder.decode(state.value);\n              if (state.headers[state.key] === undefined) {\n                state.headers[state.key] = value;\n              } else if (typeof state.headers[state.key] === \"string\") {\n                state.headers[state.key] = [state.headers[state.key], value];\n              } else {\n                ;\n                state.headers[state.key].push(value);\n              }\n              start = i;\n              state.size--;\n              if (state.crlf !== 4 && state.pairs === constMaxPairs) {\n                return error(\"TooManyHeaders\");\n              } else if (state.crlf === 3) {\n                return error(\"InvalidHeaderValue\");\n              } else if (state.crlf === 4) {\n                return reset({\n                  _tag: \"Headers\",\n                  headers: state.headers,\n                  endPosition: start - endOffset\n                });\n              }\n              state.pairs++;\n              state.key = \"\";\n              state.value = undefined;\n              state.crlf = 0;\n              state.state = State.key;\n              continue outer;\n            }\n          } else if (constValueChars[chunk[i]] !== 1) {\n            return error(\"InvalidHeaderValue\");\n          }\n        }\n        if (i === end) {\n          state.value = state.value === undefined ? chunk.subarray(start, end) : concatUint8Array(state.value, chunk.subarray(start, end));\n          return constContinue;\n        }\n      }\n    }\n    if (start > end) {\n      state.size += end - start;\n    }\n    return constContinue;\n  };\n}\n//# sourceMappingURL=headers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/multipasta/dist/esm/internal/headers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/multipasta/dist/esm/internal/multipart.js":
/*!****************************************************************!*\
  !*** ./node_modules/multipasta/dist/esm/internal/multipart.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeField: () => (/* binding */ decodeField),\n/* harmony export */   defaultIsFile: () => (/* binding */ defaultIsFile),\n/* harmony export */   make: () => (/* binding */ make)\n/* harmony export */ });\n/* harmony import */ var _contentType_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./contentType.js */ \"(rsc)/./node_modules/multipasta/dist/esm/internal/contentType.js\");\n/* harmony import */ var _headers_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./headers.js */ \"(rsc)/./node_modules/multipasta/dist/esm/internal/headers.js\");\n/* harmony import */ var _search_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./search.js */ \"(rsc)/./node_modules/multipasta/dist/esm/internal/search.js\");\n\n\n\nvar State;\n(function (State) {\n  State[State[\"headers\"] = 0] = \"headers\";\n  State[State[\"body\"] = 1] = \"body\";\n})(State || (State = {}));\nconst errInvalidDisposition = {\n  _tag: \"InvalidDisposition\"\n};\nconst errEndNotReached = {\n  _tag: \"EndNotReached\"\n};\nconst errMaxParts = {\n  _tag: \"ReachedLimit\",\n  limit: \"MaxParts\"\n};\nconst errMaxTotalSize = {\n  _tag: \"ReachedLimit\",\n  limit: \"MaxTotalSize\"\n};\nconst errMaxPartSize = {\n  _tag: \"ReachedLimit\",\n  limit: \"MaxPartSize\"\n};\nconst errMaxFieldSize = {\n  _tag: \"ReachedLimit\",\n  limit: \"MaxFieldSize\"\n};\nconst constCR = /*#__PURE__*/new TextEncoder().encode(\"\\r\\n\");\nfunction defaultIsFile(info) {\n  return info.filename !== undefined || info.contentType === \"application/octet-stream\";\n}\nfunction parseBoundary(headers) {\n  const contentType = _contentType_js__WEBPACK_IMPORTED_MODULE_0__.parse(headers[\"content-type\"]);\n  return contentType.parameters.boundary;\n}\nfunction noopOnChunk(_chunk) {}\nfunction make({\n  headers,\n  onFile: onPart,\n  onField,\n  onError,\n  onDone,\n  isFile = defaultIsFile,\n  maxParts = Infinity,\n  maxTotalSize = Infinity,\n  maxPartSize = Infinity,\n  maxFieldSize = 1024 * 1024\n}) {\n  const boundary = parseBoundary(headers);\n  if (boundary === undefined) {\n    onError({\n      _tag: \"InvalidBoundary\"\n    });\n    return {\n      write: noopOnChunk,\n      end() {}\n    };\n  }\n  const state = {\n    state: State.headers,\n    index: 0,\n    parts: 0,\n    onChunk: noopOnChunk,\n    info: undefined,\n    headerSkip: 0,\n    partSize: 0,\n    totalSize: 0,\n    isFile: false,\n    fieldChunks: [],\n    fieldSize: 0\n  };\n  function skipBody() {\n    state.state = State.body;\n    state.isFile = true;\n    state.onChunk = noopOnChunk;\n  }\n  const headerParser = _headers_js__WEBPACK_IMPORTED_MODULE_1__.make();\n  const split = _search_js__WEBPACK_IMPORTED_MODULE_2__.make(`\\r\\n--${boundary}`, function (index, chunk) {\n    if (index === 0) {\n      // data before the first boundary\n      skipBody();\n      return;\n    } else if (index !== state.index) {\n      if (state.index > 0) {\n        if (state.isFile) {\n          state.onChunk(null);\n          state.partSize = 0;\n        } else {\n          if (state.fieldChunks.length === 1) {\n            onField(state.info, state.fieldChunks[0]);\n          } else {\n            const buf = new Uint8Array(state.fieldSize);\n            let offset = 0;\n            for (let i = 0; i < state.fieldChunks.length; i++) {\n              const chunk = state.fieldChunks[i];\n              buf.set(chunk, offset);\n              offset += chunk.length;\n            }\n            onField(state.info, buf);\n          }\n          state.fieldSize = 0;\n          state.fieldChunks = [];\n        }\n      }\n      state.state = State.headers;\n      state.index = index;\n      state.headerSkip = 2; // skip the first \\r\\n\n      // trailing --\n      if (chunk[0] === 45 && chunk[1] === 45) {\n        return onDone();\n      }\n      state.parts++;\n      if (state.parts > maxParts) {\n        onError(errMaxParts);\n      }\n    }\n    if ((state.partSize += chunk.length) > maxPartSize) {\n      onError(errMaxPartSize);\n    }\n    if (state.state === State.headers) {\n      const result = headerParser(chunk, state.headerSkip);\n      state.headerSkip = 0;\n      if (result._tag === \"Continue\") {\n        return;\n      } else if (result._tag === \"Failure\") {\n        skipBody();\n        return onError({\n          _tag: \"BadHeaders\",\n          error: result\n        });\n      }\n      const contentType = _contentType_js__WEBPACK_IMPORTED_MODULE_0__.parse(result.headers[\"content-type\"]);\n      const contentDisposition = _contentType_js__WEBPACK_IMPORTED_MODULE_0__.parse(result.headers[\"content-disposition\"], true);\n      if (\"form-data\" === contentDisposition.value && !(\"name\" in contentDisposition.parameters)) {\n        skipBody();\n        return onError(errInvalidDisposition);\n      }\n      let encodedFilename;\n      if (\"filename*\" in contentDisposition.parameters) {\n        const parts = contentDisposition.parameters[\"filename*\"].split(\"''\");\n        if (parts.length === 2) {\n          encodedFilename = decodeURIComponent(parts[1]);\n        }\n      }\n      state.info = {\n        name: contentDisposition.parameters.name ?? \"\",\n        filename: encodedFilename ?? contentDisposition.parameters.filename,\n        contentType: contentType.value === \"\" ? contentDisposition.parameters.filename !== undefined ? \"application/octet-stream\" : \"text/plain\" : contentType.value,\n        contentTypeParameters: contentType.parameters,\n        contentDisposition: contentDisposition.value,\n        contentDispositionParameters: contentDisposition.parameters,\n        headers: result.headers\n      };\n      state.state = State.body;\n      state.isFile = isFile(state.info);\n      if (state.isFile) {\n        state.onChunk = onPart(state.info);\n      }\n      if (result.endPosition < chunk.length) {\n        if (state.isFile) {\n          state.onChunk(chunk.subarray(result.endPosition));\n        } else {\n          const buf = chunk.subarray(result.endPosition);\n          if ((state.fieldSize += buf.length) > maxFieldSize) {\n            onError(errMaxFieldSize);\n          }\n          state.fieldChunks.push(buf);\n        }\n      }\n    } else if (state.isFile) {\n      state.onChunk(chunk);\n    } else {\n      if ((state.fieldSize += chunk.length) > maxFieldSize) {\n        onError(errMaxFieldSize);\n      }\n      state.fieldChunks.push(chunk);\n    }\n  }, constCR);\n  return {\n    write(chunk) {\n      if ((state.totalSize += chunk.length) > maxTotalSize) {\n        return onError(errMaxTotalSize);\n      }\n      return split.write(chunk);\n    },\n    end() {\n      split.end();\n      if (state.state === State.body) {\n        onError(errEndNotReached);\n      }\n      state.state = State.headers;\n      state.index = 0;\n      state.parts = 0;\n      state.onChunk = noopOnChunk;\n      state.info = undefined;\n      state.totalSize = 0;\n      state.partSize = 0;\n      state.fieldChunks = [];\n      state.fieldSize = 0;\n    }\n  };\n}\nconst utf8Decoder = /*#__PURE__*/new TextDecoder(\"utf-8\");\nfunction getDecoder(charset) {\n  if (charset === \"utf-8\" || charset === \"utf8\" || charset === \"\") {\n    return utf8Decoder;\n  }\n  try {\n    return new TextDecoder(charset);\n  } catch (error) {\n    return utf8Decoder;\n  }\n}\nfunction decodeField(info, value) {\n  return getDecoder(info.contentTypeParameters.charset ?? \"utf-8\").decode(value);\n}\n//# sourceMappingURL=multipart.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/multipasta/dist/esm/internal/multipart.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/multipasta/dist/esm/internal/search.js":
/*!*************************************************************!*\
  !*** ./node_modules/multipasta/dist/esm/internal/search.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   make: () => (/* binding */ make)\n/* harmony export */ });\nfunction makeState(needle_) {\n  const needle = new TextEncoder().encode(needle_);\n  const needleLength = needle.length;\n  const indexes = {};\n  for (let i = 0; i < needleLength; i++) {\n    const b = needle[i];\n    if (indexes[b] === undefined) indexes[b] = [];\n    indexes[b].push(i);\n  }\n  return {\n    needle,\n    needleLength,\n    indexes,\n    firstByte: needle[0],\n    previousChunk: undefined,\n    previousChunkLength: 0,\n    matchIndex: 0\n  };\n}\nfunction make(needle, callback, seed) {\n  const state = makeState(needle);\n  if (seed !== undefined) {\n    state.previousChunk = seed;\n    state.previousChunkLength = seed.length;\n  }\n  function makeIndexOf() {\n    // on node.js use the Buffer api\n    if (\"Buffer\" in globalThis && !(\"Bun\" in globalThis || \"Deno\" in globalThis)) {\n      return function (chunk, needle, fromIndex) {\n        return Buffer.prototype.indexOf.call(chunk, needle, fromIndex);\n      };\n    }\n    const skipTable = new Uint8Array(256).fill(state.needle.length);\n    for (let i = 0, lastIndex = state.needle.length - 1; i < lastIndex; ++i) {\n      skipTable[state.needle[i]] = lastIndex - i;\n    }\n    return function (chunk, needle, fromIndex) {\n      const lengthTotal = chunk.length;\n      let i = fromIndex + state.needleLength - 1;\n      while (i < lengthTotal) {\n        for (let j = state.needleLength - 1, k = i; j >= 0 && chunk[k] === needle[j]; j--, k--) {\n          if (j === 0) return k;\n        }\n        i += skipTable[chunk[i]];\n      }\n      return -1;\n    };\n  }\n  const indexOf = makeIndexOf();\n  function write(chunk) {\n    let chunkLength = chunk.length;\n    if (state.previousChunk !== undefined) {\n      const newChunk = new Uint8Array(state.previousChunkLength + chunkLength);\n      newChunk.set(state.previousChunk);\n      newChunk.set(chunk, state.previousChunkLength);\n      chunk = newChunk;\n      chunkLength = state.previousChunkLength + chunkLength;\n      state.previousChunk = undefined;\n    }\n    if (chunkLength < state.needleLength) {\n      state.previousChunk = chunk;\n      state.previousChunkLength = chunkLength;\n      return;\n    }\n    let pos = 0;\n    while (pos < chunkLength) {\n      const match = indexOf(chunk, state.needle, pos);\n      if (match > -1) {\n        if (match > pos) {\n          callback(state.matchIndex, chunk.subarray(pos, match));\n        }\n        state.matchIndex += 1;\n        pos = match + state.needleLength;\n        continue;\n      } else if (chunk[chunkLength - 1] in state.indexes) {\n        const indexes = state.indexes[chunk[chunkLength - 1]];\n        let earliestIndex = -1;\n        for (let i = 0, len = indexes.length; i < len; i++) {\n          const index = indexes[i];\n          if (chunk[chunkLength - 1 - index] === state.firstByte && i > earliestIndex) {\n            earliestIndex = index;\n          }\n        }\n        if (earliestIndex === -1) {\n          if (pos === 0) {\n            callback(state.matchIndex, chunk);\n          } else {\n            callback(state.matchIndex, chunk.subarray(pos));\n          }\n        } else {\n          if (chunkLength - 1 - earliestIndex > pos) {\n            callback(state.matchIndex, chunk.subarray(pos, chunkLength - 1 - earliestIndex));\n          }\n          state.previousChunk = chunk.subarray(chunkLength - 1 - earliestIndex);\n          state.previousChunkLength = earliestIndex + 1;\n        }\n      } else if (pos === 0) {\n        callback(state.matchIndex, chunk);\n      } else {\n        callback(state.matchIndex, chunk.subarray(pos));\n      }\n      break;\n    }\n  }\n  function end() {\n    if (state.previousChunk !== undefined && state.previousChunk !== seed) {\n      callback(state.matchIndex, state.previousChunk);\n    }\n    state.previousChunk = seed;\n    state.previousChunkLength = seed?.length ?? 0;\n    state.matchIndex = 0;\n  }\n  return {\n    write,\n    end\n  };\n}\n//# sourceMappingURL=search.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/multipasta/dist/esm/internal/search.js\n");

/***/ })

};
;