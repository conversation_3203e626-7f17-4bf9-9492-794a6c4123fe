{"c": ["app/layout", "webpack"], "r": ["app/dashboard/properties/create/page"], "m": ["(app-pages-browser)/./app/dashboard/properties/create/page.tsx", "(app-pages-browser)/./app/dashboard/properties/create/property-form-steps.tsx", "(app-pages-browser)/./components/LanguageSwitcher.tsx", "(app-pages-browser)/./components/SimpleImageUpload.tsx", "(app-pages-browser)/./components/ThemeSwitcher.tsx", "(app-pages-browser)/./components/ui/label.tsx", "(app-pages-browser)/./components/ui/separator.tsx", "(app-pages-browser)/./components/ui/switch.tsx", "(app-pages-browser)/./components/ui/textarea.tsx", "(app-pages-browser)/./lib/uploadthing.ts", "(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-separator/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-switch/dist/index.mjs", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_check_private_redeclaration.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_apply_descriptor_get.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_apply_descriptor_set.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_extract_field_descriptor.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_private_field_get.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_private_field_init.js", "(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_private_field_set.js", "(app-pages-browser)/./node_modules/@uploadthing/mime-types/application/index.js", "(app-pages-browser)/./node_modules/@uploadthing/mime-types/audio/index.js", "(app-pages-browser)/./node_modules/@uploadthing/mime-types/dist/index.js", "(app-pages-browser)/./node_modules/@uploadthing/mime-types/image/index.js", "(app-pages-browser)/./node_modules/@uploadthing/mime-types/text/index.js", "(app-pages-browser)/./node_modules/@uploadthing/mime-types/video/index.js", "(app-pages-browser)/./node_modules/@uploadthing/react/dist/button-client-BLNyMUF0.js", "(app-pages-browser)/./node_modules/@uploadthing/react/dist/dropzone-client-veu9GOWx.js", "(app-pages-browser)/./node_modules/@uploadthing/react/dist/index.js", "(app-pages-browser)/./node_modules/@uploadthing/react/dist/uploader-client-BJ5fn8fN.js", "(app-pages-browser)/./node_modules/@uploadthing/shared/dist/index.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Array.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Context.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Effectable.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Either.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Encoding.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Equal.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Equivalence.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Function.js", "(app-pages-browser)/./node_modules/effect/dist/esm/GlobalValue.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Hash.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Inspectable.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Iterable.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Micro.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Option.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Order.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Pipeable.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Predicate.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Record.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Redacted.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Tuple.js", "(app-pages-browser)/./node_modules/effect/dist/esm/Utils.js", "(app-pages-browser)/./node_modules/effect/dist/esm/internal/array.js", "(app-pages-browser)/./node_modules/effect/dist/esm/internal/context.js", "(app-pages-browser)/./node_modules/effect/dist/esm/internal/doNotation.js", "(app-pages-browser)/./node_modules/effect/dist/esm/internal/effectable.js", "(app-pages-browser)/./node_modules/effect/dist/esm/internal/either.js", "(app-pages-browser)/./node_modules/effect/dist/esm/internal/encoding/base64.js", "(app-pages-browser)/./node_modules/effect/dist/esm/internal/encoding/base64Url.js", "(app-pages-browser)/./node_modules/effect/dist/esm/internal/encoding/common.js", "(app-pages-browser)/./node_modules/effect/dist/esm/internal/encoding/hex.js", "(app-pages-browser)/./node_modules/effect/dist/esm/internal/errors.js", "(app-pages-browser)/./node_modules/effect/dist/esm/internal/opCodes/effect.js", "(app-pages-browser)/./node_modules/effect/dist/esm/internal/option.js", "(app-pages-browser)/./node_modules/effect/dist/esm/internal/redacted.js", "(app-pages-browser)/./node_modules/effect/dist/esm/internal/version.js", "(app-pages-browser)/./node_modules/file-selector/dist/es5/file-selector.js", "(app-pages-browser)/./node_modules/file-selector/dist/es5/file.js", "(app-pages-browser)/./node_modules/file-selector/dist/es5/index.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/languages.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Cdashboard%5C%5Cproperties%5C%5Ccreate%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./node_modules/sonner/dist/index.mjs", "(app-pages-browser)/./node_modules/sqids/esm/sqids.js", "(app-pages-browser)/./node_modules/uploadthing/client/index.js", "(app-pages-browser)/./node_modules/uploadthing/dist/_internal/deferred.js", "(app-pages-browser)/./node_modules/uploadthing/dist/_internal/deprecations.js", "(app-pages-browser)/./node_modules/uploadthing/dist/_internal/upload-browser.js", "(app-pages-browser)/./node_modules/uploadthing/dist/_internal/ut-reporter.js"]}