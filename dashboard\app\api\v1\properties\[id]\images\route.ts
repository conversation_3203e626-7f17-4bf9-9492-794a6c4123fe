import { NextRequest, NextResponse } from 'next/server';

/**
 * Auto-save images for a specific property
 * PUT /api/v1/properties/[id]/images
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { images } = await request.json();
    const propertyId = params.id;

    if (!propertyId) {
      return NextResponse.json(
        { success: false, error: 'Property ID is required' },
        { status: 400 }
      );
    }

    if (!Array.isArray(images)) {
      return NextResponse.json(
        { success: false, error: 'Images must be an array' },
        { status: 400 }
      );
    }

    // Try to update via backend API
    try {
      const backendResponse = await fetch(`${process.env.BACKEND_URL || 'http://localhost:5000'}/api/v1/properties/${propertyId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ images }),
      });

      if (backendResponse.ok) {
        const data = await backendResponse.json();
        return NextResponse.json({
          success: true,
          data: data.data,
          message: 'Images auto-saved successfully'
        });
      } else {
        throw new Error('Backend API failed');
      }
    } catch (backendError) {
      console.log('Backend not available, using mock response for auto-save');
      
      // Mock successful response for development
      return NextResponse.json({
        success: true,
        data: {
          id: propertyId,
          images,
          updatedAt: new Date().toISOString()
        },
        message: 'Images auto-saved successfully (mock)'
      });
    }
  } catch (error: any) {
    console.error('Error auto-saving images:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to auto-save images',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * Get images for a specific property
 * GET /api/v1/properties/[id]/images
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const propertyId = params.id;

    if (!propertyId) {
      return NextResponse.json(
        { success: false, error: 'Property ID is required' },
        { status: 400 }
      );
    }

    // Try to fetch from backend API
    try {
      const backendResponse = await fetch(`${process.env.BACKEND_URL || 'http://localhost:5000'}/api/v1/properties/${propertyId}`);

      if (backendResponse.ok) {
        const data = await backendResponse.json();
        return NextResponse.json({
          success: true,
          data: {
            images: data.data?.images || []
          }
        });
      } else {
        throw new Error('Backend API failed');
      }
    } catch (backendError) {
      console.log('Backend not available, using mock response');
      
      // Mock response for development
      return NextResponse.json({
        success: true,
        data: {
          images: ['/placeholder.jpg']
        }
      });
    }
  } catch (error: any) {
    console.error('Error fetching property images:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch property images',
        message: error.message 
      },
      { status: 500 }
    );
  }
}
