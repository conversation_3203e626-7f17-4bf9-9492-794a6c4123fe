'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { DashboardSlider, CompactDashboardSlider } from '@/components/DashboardSlider';
import { LanguageSwitcher } from '@/components/LanguageSwitcher';
import { ThemeSwitcher } from '@/components/ThemeSwitcher';
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage';
import { useTheme } from '@/hooks/useTheme';
import { 
  Home, 
  TrendingUp, 
  Users, 
  Calendar, 
  Plus, 
  Eye, 
  Edit,
  BarChart3,
  MapPin,
  Star
} from 'lucide-react';
import Link from 'next/link';

export default function DashboardPage() {
  const { language, isArabic } = useSimpleLanguage();
  const { isDark } = useTheme();

  // Bilingual translations
  const t = {
    ar: {
      dashboard: 'لوحة التحكم',
      welcome: 'مرحباً بك',
      subtitle: 'إدارة شاملة لجميع عقاراتك',
      quickActions: 'الإجراءات السريعة',
      statistics: 'الإحصائيات',
      recentActivity: 'النشاط الأخير',
      properties: 'العقارات',
      clients: 'العملاء',
      appointments: 'المواعيد',
      analytics: 'التحليلات',
      addProperty: 'إضافة عقار',
      viewProperties: 'عرض العقارات',
      manageClients: 'إدارة العملاء',
      scheduleAppointment: 'جدولة موعد',
      viewAnalytics: 'عرض التحليلات',
      totalProperties: 'إجمالي العقارات',
      activeListings: 'الإعلانات النشطة',
      totalClients: 'إجمالي العملاء',
      thisMonth: 'هذا الشهر',
      viewAll: 'عرض الكل',
      manage: 'إدارة',
    },
    en: {
      dashboard: 'Dashboard',
      welcome: 'Welcome',
      subtitle: 'Comprehensive management for all your properties',
      quickActions: 'Quick Actions',
      statistics: 'Statistics',
      recentActivity: 'Recent Activity',
      properties: 'Properties',
      clients: 'Clients',
      appointments: 'Appointments',
      analytics: 'Analytics',
      addProperty: 'Add Property',
      viewProperties: 'View Properties',
      manageClients: 'Manage Clients',
      scheduleAppointment: 'Schedule Appointment',
      viewAnalytics: 'View Analytics',
      totalProperties: 'Total Properties',
      activeListings: 'Active Listings',
      totalClients: 'Total Clients',
      thisMonth: 'This Month',
      viewAll: 'View All',
      manage: 'Manage',
    }
  };

  const translations = t[language];

  // Quick action items
  const quickActions = [
    {
      title: translations.addProperty,
      icon: Plus,
      href: '/dashboard/properties/create',
      gradient: 'from-emerald-500 to-teal-600',
      description: language === 'ar' ? 'إضافة عقار جديد' : 'Add a new property'
    },
    {
      title: translations.viewProperties,
      icon: Eye,
      href: '/dashboard/properties',
      gradient: 'from-blue-500 to-indigo-600',
      description: language === 'ar' ? 'عرض جميع العقارات' : 'View all properties'
    },
    {
      title: translations.manageClients,
      icon: Users,
      href: '/dashboard/clients',
      gradient: 'from-purple-500 to-pink-600',
      description: language === 'ar' ? 'إدارة العملاء' : 'Manage clients'
    },
    {
      title: translations.viewAnalytics,
      icon: BarChart3,
      href: '/dashboard/analytics',
      gradient: 'from-orange-500 to-red-600',
      description: language === 'ar' ? 'عرض التحليلات' : 'View analytics'
    }
  ];

  // Statistics data
  const stats = [
    {
      title: translations.totalProperties,
      value: '156',
      icon: Home,
      gradient: 'from-emerald-500 to-teal-600',
      change: '+12%'
    },
    {
      title: translations.activeListings,
      value: '89',
      icon: Star,
      gradient: 'from-blue-500 to-indigo-600',
      change: '+8%'
    },
    {
      title: translations.totalClients,
      value: '342',
      icon: Users,
      gradient: 'from-purple-500 to-pink-600',
      change: '+15%'
    },
    {
      title: translations.thisMonth,
      value: '24',
      icon: Calendar,
      gradient: 'from-orange-500 to-red-600',
      change: '+5%'
    }
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 ${isArabic ? 'rtl' : 'ltr'}`} dir={isArabic ? 'rtl' : 'ltr'}>
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className={`flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8 gap-6 ${isArabic ? 'lg:flex-row-reverse' : ''}`}>
          <div className={`space-y-2 ${isArabic ? 'text-right' : 'text-left'}`}>
            <h1 className="text-4xl font-black bg-gradient-to-r from-slate-900 to-slate-700 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
              {translations.dashboard}
            </h1>
            <p className="text-lg text-slate-600 dark:text-slate-400">
              {translations.subtitle}
            </p>
          </div>
          
          {/* Language and Theme Switchers */}
          <div className={`flex items-center gap-4 ${isArabic ? 'flex-row-reverse' : ''}`}>
            <LanguageSwitcher />
            <ThemeSwitcher />
          </div>
        </div>

        {/* Main Dashboard Slider */}
        <div className="mb-12">
          <DashboardSlider />
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <Card key={index} className="overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border-0 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className={`flex items-center justify-between ${isArabic ? 'flex-row-reverse' : ''}`}>
                    <div className={`${isArabic ? 'text-right' : 'text-left'}`}>
                      <p className="text-sm font-medium text-slate-600 dark:text-slate-400 mb-1">
                        {stat.title}
                      </p>
                      <p className="text-3xl font-black text-slate-900 dark:text-white">
                        {stat.value}
                      </p>
                      <p className="text-sm font-medium text-emerald-600 dark:text-emerald-400 mt-1">
                        {stat.change}
                      </p>
                    </div>
                    <div className={`w-16 h-16 bg-gradient-to-br ${stat.gradient} rounded-2xl flex items-center justify-center shadow-lg`}>
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Quick Actions */}
        <div className="mb-12">
          <h2 className={`text-2xl font-bold text-slate-900 dark:text-white mb-6 ${isArabic ? 'text-right' : 'text-left'}`}>
            {translations.quickActions}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action, index) => {
              const IconComponent = action.icon;
              return (
                <Link key={index} href={action.href}>
                  <Card className="group cursor-pointer overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border-0 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:scale-105">
                    <CardContent className="p-6">
                      <div className={`flex flex-col items-center text-center space-y-4 ${isArabic ? 'text-right' : 'text-left'}`}>
                        <div className={`w-20 h-20 bg-gradient-to-br ${action.gradient} rounded-3xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                          <IconComponent className="h-10 w-10 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-2">
                            {action.title}
                          </h3>
                          <p className="text-sm text-slate-600 dark:text-slate-400">
                            {action.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              );
            })}
          </div>
        </div>

        {/* Compact Slider for Mobile */}
        <div className="lg:hidden mb-8">
          <h2 className={`text-2xl font-bold text-slate-900 dark:text-white mb-6 ${isArabic ? 'text-right' : 'text-left'}`}>
            {translations.recentActivity}
          </h2>
          <CompactDashboardSlider />
        </div>
      </div>
    </div>
  );
}
