'use client';

import { useEffect } from 'react';

/**
 * Dark Mode Provider Component
 * Ensures dark mode is applied immediately on page load
 */
export function DarkModeProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Apply dark mode immediately
    const applyDarkMode = () => {
      // Check for saved theme preference, default to dark
      const savedTheme = localStorage.getItem('properties-theme') || 'dark';
      
      if (savedTheme === 'dark') {
        document.documentElement.classList.add('dark');
        document.documentElement.classList.remove('light');
      } else {
        document.documentElement.classList.add('light');
        document.documentElement.classList.remove('dark');
      }
      
      // Set color scheme
      document.documentElement.style.colorScheme = savedTheme;
    };

    // Apply immediately
    applyDarkMode();
    
    // Also apply on storage change (for cross-tab sync)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'properties-theme') {
        applyDarkMode();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return <>{children}</>;
}

/**
 * Script to inject dark mode before hydration
 * This prevents flash of light mode
 */
export function DarkModeScript() {
  const script = `
    (function() {
      try {
        const theme = localStorage.getItem('properties-theme') || 'dark';
        if (theme === 'dark') {
          document.documentElement.classList.add('dark');
          document.documentElement.style.colorScheme = 'dark';
        } else {
          document.documentElement.classList.add('light');
          document.documentElement.style.colorScheme = 'light';
        }
      } catch (e) {
        // Fallback to dark mode
        document.documentElement.classList.add('dark');
        document.documentElement.style.colorScheme = 'dark';
      }
    })();
  `;

  return (
    <script
      dangerouslySetInnerHTML={{ __html: script }}
      suppressHydrationWarning
    />
  );
}
