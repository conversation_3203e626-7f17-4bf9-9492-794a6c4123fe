'use client';

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Home, TrendingUp, Users, Calendar, Star, MapPin } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage';
import { useTheme } from '@/hooks/useTheme';

/**
 * Beautiful Dashboard Slider Component
 * Features bilingual support, dark mode, and smooth animations
 */
export function DashboardSlider() {
  const { language, isArabic } = useSimpleLanguage();
  const { isDark } = useTheme();
  const [currentSlide, setCurrentSlide] = useState(0);

  // Bilingual slide content
  const slides = [
    {
      id: 1,
      title: {
        ar: 'مرحباً بك في نظام إدارة العقارات',
        en: 'Welcome to Property Management System'
      },
      subtitle: {
        ar: 'إدارة شاملة وذكية لجميع عقاراتك',
        en: 'Comprehensive and smart management for all your properties'
      },
      description: {
        ar: 'نظام متطور لإدارة العقارات مع دعم ثنائي اللغة والوضع المظلم',
        en: 'Advanced property management system with bilingual support and dark mode'
      },
      icon: Home,
      gradient: 'from-emerald-500 to-teal-600',
      bgGradient: 'from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20'
    },
    {
      id: 2,
      title: {
        ar: 'تحليلات وإحصائيات متقدمة',
        en: 'Advanced Analytics & Statistics'
      },
      subtitle: {
        ar: 'رؤى عميقة لأداء عقاراتك',
        en: 'Deep insights into your property performance'
      },
      description: {
        ar: 'تتبع الأرباح والمبيعات والعملاء مع تقارير تفصيلية',
        en: 'Track profits, sales, and customers with detailed reports'
      },
      icon: TrendingUp,
      gradient: 'from-blue-500 to-indigo-600',
      bgGradient: 'from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20'
    },
    {
      id: 3,
      title: {
        ar: 'إدارة العملاء والمواعيد',
        en: 'Client & Appointment Management'
      },
      subtitle: {
        ar: 'تنظيم مثالي لعملائك ومواعيدك',
        en: 'Perfect organization for your clients and appointments'
      },
      description: {
        ar: 'نظام شامل لإدارة العملاء وجدولة المواعيد والمتابعة',
        en: 'Comprehensive system for client management, scheduling, and follow-up'
      },
      icon: Users,
      gradient: 'from-purple-500 to-pink-600',
      bgGradient: 'from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20'
    },
    {
      id: 4,
      title: {
        ar: 'مواقع مميزة وعقارات فاخرة',
        en: 'Premium Locations & Luxury Properties'
      },
      subtitle: {
        ar: 'اكتشف أفضل العقارات في أرقى المواقع',
        en: 'Discover the best properties in premium locations'
      },
      description: {
        ar: 'مجموعة مختارة من العقارات الفاخرة في أفضل المواقع',
        en: 'Curated collection of luxury properties in the best locations'
      },
      icon: MapPin,
      gradient: 'from-orange-500 to-red-600',
      bgGradient: 'from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20'
    }
  ];

  // Auto-slide functionality
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(timer);
  }, [slides.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const currentSlideData = slides[currentSlide];
  const IconComponent = currentSlideData.icon;

  return (
    <div className={`relative overflow-hidden rounded-3xl shadow-2xl ${language === 'ar' ? 'rtl' : 'ltr'}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      {/* Main Slider Container */}
      <div className={`relative h-96 bg-gradient-to-br ${currentSlideData.bgGradient} transition-all duration-1000 ease-in-out`}>
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full bg-gradient-to-br from-white/20 via-transparent to-white/10"></div>
        </div>

        {/* Content */}
        <div className={`relative h-full flex items-center ${isArabic ? 'flex-row-reverse' : 'flex-row'} p-8 lg:p-12`}>
          {/* Icon Section */}
          <div className="flex-shrink-0">
            <div className={`w-24 h-24 lg:w-32 lg:h-32 bg-gradient-to-br ${currentSlideData.gradient} rounded-3xl flex items-center justify-center shadow-2xl transform transition-all duration-500 hover:scale-110`}>
              <IconComponent className="h-12 w-12 lg:h-16 lg:w-16 text-white" />
            </div>
          </div>

          {/* Text Content */}
          <div className={`flex-1 ${isArabic ? 'mr-8 lg:mr-12 text-right' : 'ml-8 lg:ml-12 text-left'} space-y-4`}>
            <h2 className="text-3xl lg:text-4xl font-black text-slate-800 dark:text-white leading-tight">
              {currentSlideData.title[language]}
            </h2>
            <h3 className="text-xl lg:text-2xl font-bold text-slate-600 dark:text-slate-300">
              {currentSlideData.subtitle[language]}
            </h3>
            <p className="text-lg text-slate-600 dark:text-slate-400 leading-relaxed max-w-2xl">
              {currentSlideData.description[language]}
            </p>

            {/* Action Buttons */}
            <div className={`flex items-center gap-4 pt-4 ${isArabic ? 'flex-row-reverse' : 'flex-row'}`}>
              <Button className={`px-6 py-3 bg-gradient-to-r ${currentSlideData.gradient} hover:shadow-lg transition-all duration-300 text-white font-bold rounded-xl`}>
                {language === 'ar' ? 'اكتشف المزيد' : 'Discover More'}
              </Button>
              <Button variant="outline" className="px-6 py-3 border-2 border-slate-300 dark:border-slate-600 hover:bg-slate-100 dark:hover:bg-slate-800 transition-all duration-300 rounded-xl">
                {language === 'ar' ? 'تعرف أكثر' : 'Learn More'}
              </Button>
            </div>
          </div>
        </div>

        {/* Navigation Arrows */}
        <Button
          variant="ghost"
          size="sm"
          onClick={prevSlide}
          className={`absolute top-1/2 transform -translate-y-1/2 ${isArabic ? 'right-4' : 'left-4'} w-12 h-12 rounded-full bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:bg-white dark:hover:bg-slate-800 shadow-lg transition-all duration-300`}
        >
          <ChevronLeft className={`h-6 w-6 text-slate-700 dark:text-slate-300 ${isArabic ? 'rotate-180' : ''}`} />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={nextSlide}
          className={`absolute top-1/2 transform -translate-y-1/2 ${isArabic ? 'left-4' : 'right-4'} w-12 h-12 rounded-full bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:bg-white dark:hover:bg-slate-800 shadow-lg transition-all duration-300`}
        >
          <ChevronRight className={`h-6 w-6 text-slate-700 dark:text-slate-300 ${isArabic ? 'rotate-180' : ''}`} />
        </Button>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex items-center gap-3">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide
                ? `bg-gradient-to-r ${currentSlideData.gradient} shadow-lg scale-125`
                : 'bg-white/60 dark:bg-slate-600/60 hover:bg-white/80 dark:hover:bg-slate-600/80'
            }`}
          />
        ))}
      </div>

      {/* Slide Counter */}
      <div className={`absolute top-6 ${isArabic ? 'left-6' : 'right-6'} px-4 py-2 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-full shadow-lg`}>
        <span className="text-sm font-bold text-slate-700 dark:text-slate-300">
          {currentSlide + 1} / {slides.length}
        </span>
      </div>
    </div>
  );
}

/**
 * Compact Dashboard Slider for smaller spaces
 */
export function CompactDashboardSlider() {
  const { language, isArabic } = useSimpleLanguage();
  const [currentSlide, setCurrentSlide] = useState(0);

  const compactSlides = [
    {
      title: { ar: 'إدارة العقارات', en: 'Property Management' },
      icon: Home,
      gradient: 'from-emerald-500 to-teal-600'
    },
    {
      title: { ar: 'التحليلات', en: 'Analytics' },
      icon: TrendingUp,
      gradient: 'from-blue-500 to-indigo-600'
    },
    {
      title: { ar: 'العملاء', en: 'Clients' },
      icon: Users,
      gradient: 'from-purple-500 to-pink-600'
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % compactSlides.length);
    }, 3000);

    return () => clearInterval(timer);
  }, [compactSlides.length]);

  const currentSlideData = compactSlides[currentSlide];
  const IconComponent = currentSlideData.icon;

  return (
    <Card className="overflow-hidden shadow-lg">
      <CardContent className="p-6">
        <div className={`flex items-center gap-4 ${isArabic ? 'flex-row-reverse' : 'flex-row'}`}>
          <div className={`w-16 h-16 bg-gradient-to-br ${currentSlideData.gradient} rounded-2xl flex items-center justify-center shadow-lg`}>
            <IconComponent className="h-8 w-8 text-white" />
          </div>
          <div className={`flex-1 ${isArabic ? 'text-right' : 'text-left'}`}>
            <h3 className="text-xl font-bold text-slate-800 dark:text-white">
              {currentSlideData.title[language]}
            </h3>
            <div className="flex items-center gap-1 mt-2">
              {compactSlides.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-all duration-300 ${
                    index === currentSlide ? `bg-gradient-to-r ${currentSlideData.gradient}` : 'bg-slate-300 dark:bg-slate-600'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
