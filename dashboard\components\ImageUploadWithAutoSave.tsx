'use client';

import { useState, useCallback, useEffect } from 'react';
import { Upload, X, Image as ImageIcon, Loader2, Check, AlertCircle, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage';
import { UploadDropzone } from '@/lib/uploadthing';
import { toast } from 'sonner';

interface ImageUploadWithAutoSaveProps {
  images: string[];
  onImagesChange: (images: string[]) => void;
  onAutoSave?: (images: string[]) => Promise<void>;
  propertyId?: string;
  maxImages?: number;
  disabled?: boolean;
}

interface UploadState {
  isUploading: boolean;
  progress: number;
  uploadedCount: number;
  totalCount: number;
  error?: string;
}

export function ImageUploadWithAutoSave({
  images,
  onImagesChange,
  onAutoSave,
  propertyId,
  maxImages = 10,
  disabled = false
}: ImageUploadWithAutoSaveProps) {
  const { language, isArabic } = useSimpleLanguage();
  const [uploadState, setUploadState] = useState<UploadState>({
    isUploading: false,
    progress: 0,
    uploadedCount: 0,
    totalCount: 0
  });
  const [autoSaveState, setAutoSaveState] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

  // Bilingual translations
  const t = {
    ar: {
      uploadImages: 'رفع صور العقار',
      dragDropImages: 'اسحب وأفلت الصور هنا، أو انقر للاختيار',
      mainImage: 'الصورة الرئيسية',
      additionalImages: 'الصور الإضافية',
      setAsMain: 'تعيين كصورة رئيسية',
      removeImage: 'حذف الصورة',
      uploading: 'جاري الرفع...',
      uploadComplete: 'تم الرفع بنجاح',
      uploadError: 'فشل في رفع الصورة',
      autoSaving: 'جاري الحفظ التلقائي...',
      autoSaved: 'تم الحفظ التلقائي',
      autoSaveError: 'فشل في الحفظ التلقائي',
      maxImagesReached: `الحد الأقصى ${maxImages} صور`,
      noImages: 'لم يتم رفع صور بعد',
      imageGallery: 'معرض الصور',
      uploadProgress: 'تقدم الرفع',
      filesSelected: 'ملفات محددة',
      of: 'من',
    },
    en: {
      uploadImages: 'Upload Property Images',
      dragDropImages: 'Drag and drop images here, or click to select',
      mainImage: 'Main Image',
      additionalImages: 'Additional Images',
      setAsMain: 'Set as Main Image',
      removeImage: 'Remove Image',
      uploading: 'Uploading...',
      uploadComplete: 'Upload Complete',
      uploadError: 'Upload Failed',
      autoSaving: 'Auto-saving...',
      autoSaved: 'Auto-saved',
      autoSaveError: 'Auto-save Failed',
      maxImagesReached: `Maximum ${maxImages} images`,
      noImages: 'No images uploaded yet',
      imageGallery: 'Image Gallery',
      uploadProgress: 'Upload Progress',
      filesSelected: 'files selected',
      of: 'of',
    }
  };

  const translations = t[language];

  // Auto-save when images change
  useEffect(() => {
    if (onAutoSave && images.length > 0 && autoSaveState === 'idle') {
      const autoSaveImages = async () => {
        setAutoSaveState('saving');
        try {
          await onAutoSave(images);
          setAutoSaveState('saved');
          toast.success(translations.autoSaved);
          
          // Reset to idle after 2 seconds
          setTimeout(() => setAutoSaveState('idle'), 2000);
        } catch (error) {
          console.error('Auto-save failed:', error);
          setAutoSaveState('error');
          toast.error(translations.autoSaveError);
          
          // Reset to idle after 3 seconds
          setTimeout(() => setAutoSaveState('idle'), 3000);
        }
      };

      // Debounce auto-save
      const timer = setTimeout(autoSaveImages, 1000);
      return () => clearTimeout(timer);
    }
  }, [images, onAutoSave, autoSaveState, translations.autoSaved, translations.autoSaveError]);

  const handleUploadComplete = useCallback((res: any[]) => {
    if (res && res.length > 0) {
      const newImageUrls = res.map(file => file.url);
      const updatedImages = [...images, ...newImageUrls];
      
      // Respect max images limit
      const finalImages = updatedImages.slice(0, maxImages);
      
      onImagesChange(finalImages);
      
      setUploadState({
        isUploading: false,
        progress: 100,
        uploadedCount: res.length,
        totalCount: res.length
      });

      toast.success(`${translations.uploadComplete} (${res.length} ${translations.filesSelected})`);
      
      // Reset upload state after 2 seconds
      setTimeout(() => {
        setUploadState({
          isUploading: false,
          progress: 0,
          uploadedCount: 0,
          totalCount: 0
        });
      }, 2000);
    }
  }, [images, onImagesChange, maxImages, translations.uploadComplete, translations.filesSelected]);

  const handleUploadError = useCallback((error: Error) => {
    console.error('Upload error:', error);
    setUploadState(prev => ({ ...prev, isUploading: false, error: error.message }));
    toast.error(`${translations.uploadError}: ${error.message}`);
  }, [translations.uploadError]);

  const handleUploadBegin = useCallback(() => {
    setUploadState({
      isUploading: true,
      progress: 0,
      uploadedCount: 0,
      totalCount: 0,
      error: undefined
    });
  }, []);

  const removeImage = useCallback((index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    onImagesChange(newImages);
    toast.success(translations.removeImage);
  }, [images, onImagesChange, translations.removeImage]);

  const setMainImage = useCallback((index: number) => {
    if (index === 0) return; // Already main image
    
    const newImages = [...images];
    const [mainImage] = newImages.splice(index, 1);
    newImages.unshift(mainImage);
    
    onImagesChange(newImages);
    toast.success(translations.setAsMain);
  }, [images, onImagesChange, translations.setAsMain]);

  const canUploadMore = images.length < maxImages;

  return (
    <div className={`space-y-6 ${isArabic ? 'rtl' : 'ltr'}`} dir={isArabic ? 'rtl' : 'ltr'}>
      {/* Upload Section */}
      {canUploadMore && !disabled && (
        <div className="space-y-4">
          <div className={`flex items-center justify-between ${isArabic ? 'flex-row-reverse' : ''}`}>
            <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
              {translations.uploadImages}
            </h3>
            <Badge variant="outline" className="text-sm">
              {images.length} {translations.of} {maxImages}
            </Badge>
          </div>

          {/* Upload Dropzone */}
          <div className="relative border-2 border-dashed border-emerald-300 dark:border-emerald-600 rounded-xl p-8 hover:border-emerald-400 dark:hover:border-emerald-500 transition-all duration-300 bg-gradient-to-br from-emerald-50/50 to-teal-50/50 dark:from-emerald-900/20 dark:to-teal-900/20 hover:shadow-lg">
            <div className="text-center">
              <Upload className="mx-auto h-12 w-12 text-emerald-500 mb-4" />
              <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                {translations.uploadImages}
              </h4>
              <p className="text-sm text-slate-600 dark:text-slate-400 mb-6">
                {translations.dragDropImages}
              </p>
            </div>

            <UploadDropzone
              endpoint="propertyImageUploader"
              onClientUploadComplete={handleUploadComplete}
              onUploadError={handleUploadError}
              onUploadBegin={handleUploadBegin}
              className="border-0 bg-transparent"
            />

            {/* Upload Progress */}
            {uploadState.isUploading && (
              <div className="absolute inset-0 bg-white/90 dark:bg-slate-900/90 backdrop-blur-sm rounded-xl flex items-center justify-center">
                <div className="text-center space-y-4 w-full max-w-xs">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto text-emerald-500" />
                  <div>
                    <p className="text-sm font-medium text-slate-900 dark:text-white mb-2">
                      {translations.uploading}
                    </p>
                    <Progress value={uploadState.progress} className="w-full" />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Auto-Save Status */}
      {autoSaveState !== 'idle' && (
        <div className={`flex items-center gap-2 p-3 rounded-lg ${
          autoSaveState === 'saving' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' :
          autoSaveState === 'saved' ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300' :
          'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
        } ${isArabic ? 'flex-row-reverse' : ''}`}>
          {autoSaveState === 'saving' && <Loader2 className="h-4 w-4 animate-spin" />}
          {autoSaveState === 'saved' && <Check className="h-4 w-4" />}
          {autoSaveState === 'error' && <AlertCircle className="h-4 w-4" />}
          <span className="text-sm font-medium">
            {autoSaveState === 'saving' && translations.autoSaving}
            {autoSaveState === 'saved' && translations.autoSaved}
            {autoSaveState === 'error' && translations.autoSaveError}
          </span>
        </div>
      )}

      {/* Image Gallery */}
      {images.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
            {translations.imageGallery}
          </h3>

          {/* Main Image */}
          <div className="relative">
            <div className="aspect-video bg-slate-100 dark:bg-slate-800 rounded-xl overflow-hidden shadow-lg">
              <img
                src={images[0]}
                alt="Main property image"
                className="w-full h-full object-cover"
              />
              <div className={`absolute top-4 ${isArabic ? 'right-4' : 'left-4'}`}>
                <Badge className="bg-emerald-600 text-white shadow-lg flex items-center gap-1">
                  <Star className="h-3 w-3" />
                  {translations.mainImage}
                </Badge>
              </div>
              <Button
                type="button"
                variant="destructive"
                size="sm"
                className={`absolute top-4 ${isArabic ? 'left-4' : 'right-4'} h-8 w-8 p-0 rounded-full shadow-lg hover:shadow-xl transition-all duration-200`}
                onClick={() => removeImage(0)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Additional Images */}
          {images.length > 1 && (
            <div className="space-y-3">
              <h4 className="text-md font-medium text-slate-700 dark:text-slate-300">
                {translations.additionalImages}
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {images.slice(1).map((image, index) => (
                  <div key={index + 1} className="relative group">
                    <div className="aspect-square bg-slate-100 dark:bg-slate-800 rounded-lg overflow-hidden shadow-md group-hover:shadow-lg transition-shadow duration-200">
                      <img
                        src={image}
                        alt={`Property image ${index + 2}`}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
                          <Button
                            type="button"
                            variant="secondary"
                            size="sm"
                            className="h-8 w-8 p-0 rounded-full shadow-lg"
                            onClick={() => setMainImage(index + 1)}
                          >
                            <Star className="h-3 w-3" />
                          </Button>
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            className="h-8 w-8 p-0 rounded-full shadow-lg"
                            onClick={() => removeImage(index + 1)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* No Images State */}
      {images.length === 0 && (
        <div className="text-center py-12 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-xl bg-slate-50 dark:bg-slate-800/50">
          <ImageIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600 dark:text-slate-400">
            {translations.noImages}
          </p>
        </div>
      )}

      {/* Max Images Warning */}
      {images.length >= maxImages && (
        <div className="p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
          <p className="text-amber-800 dark:text-amber-200 text-sm">
            {translations.maxImagesReached}
          </p>
        </div>
      )}
    </div>
  );
}
