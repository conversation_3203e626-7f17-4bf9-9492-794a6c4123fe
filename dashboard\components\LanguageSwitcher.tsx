'use client';

import { Button } from '@/components/ui/button';
import { Globe, Languages } from 'lucide-react';
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage';

/**
 * Language switcher component for bilingual support
 * Supports Arabic (default) and English with smooth transitions
 */
export function LanguageSwitcher() {
  const { language, setLanguage, isArabic } = useSimpleLanguage();

  const toggleLanguage = () => {
    setLanguage(language === 'ar' ? 'en' : 'ar');
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={toggleLanguage}
      className={`
        flex items-center gap-2 px-4 py-2 h-10
        bg-white/80 dark:bg-slate-800/80 backdrop-blur-md
        border-2 border-slate-200 dark:border-slate-700
        hover:bg-white dark:hover:bg-slate-800
        hover:border-emerald-300 dark:hover:border-emerald-600
        hover:shadow-lg transition-all duration-300
        text-slate-700 dark:text-slate-300
        rounded-xl font-medium
        ${isArabic ? 'flex-row-reverse' : 'flex-row'}
      `}
      dir={isArabic ? 'rtl' : 'ltr'}
    >
      <div className="flex items-center gap-2">
        <Languages className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
        <span className="font-bold">
          {language === 'ar' ? 'العربية' : 'English'}
        </span>
      </div>
      <div className="w-px h-4 bg-slate-300 dark:bg-slate-600"></div>
      <span className="text-xs text-slate-500 dark:text-slate-400">
        {language === 'ar' ? 'EN' : 'عر'}
      </span>
    </Button>
  );
}

/**
 * Compact language switcher for smaller spaces
 */
export function CompactLanguageSwitcher() {
  const { language, setLanguage } = useSimpleLanguage();

  const toggleLanguage = () => {
    setLanguage(language === 'ar' ? 'en' : 'ar');
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleLanguage}
      className="
        w-12 h-12 p-0 rounded-xl
        bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm
        hover:bg-white/90 dark:hover:bg-slate-800/90
        border border-slate-200 dark:border-slate-700
        hover:border-emerald-300 dark:hover:border-emerald-600
        transition-all duration-300
      "
    >
      <div className="flex flex-col items-center">
        <Globe className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
        <span className="text-xs font-bold text-slate-600 dark:text-slate-400">
          {language === 'ar' ? 'EN' : 'عر'}
        </span>
      </div>
    </Button>
  );
}
