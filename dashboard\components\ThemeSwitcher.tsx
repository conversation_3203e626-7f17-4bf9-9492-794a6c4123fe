'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Moon, Sun, Palette } from 'lucide-react';
import { useTheme } from '@/hooks/useTheme';
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage';

/**
 * Dark mode indicator component (dark mode only)
 * Shows current dark mode status without toggle option
 */
export function ThemeSwitcher() {
  const { language, isArabic } = useSimpleLanguage();

  const themeText = {
    ar: {
      dark: 'الوضع المظلم',
      active: 'مُفعل'
    },
    en: {
      dark: 'Dark Mode',
      active: 'Active'
    }
  };

  const t = themeText[language];

  return (
    <div
      className={`
        flex items-center gap-2 px-4 py-2 h-10
        bg-slate-800/90 backdrop-blur-md
        border-2 border-slate-700
        shadow-lg
        text-slate-300
        rounded-xl font-medium
        ${isArabic ? 'flex-row-reverse' : 'flex-row'}
      `}
      dir={isArabic ? 'rtl' : 'ltr'}
    >
      <div className="flex items-center gap-2">
        <Moon className="h-4 w-4 text-amber-400" />
        <span className="font-bold">
          {t.dark}
        </span>
        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        <span className="text-xs text-green-400">
          {t.active}
        </span>
      </div>
    </div>
  );
}

/**
 * Compact dark mode indicator for smaller spaces
 */
export function CompactThemeSwitcher() {
  return (
    <div
      className="
        w-12 h-12 p-0 rounded-xl
        bg-slate-800/80 backdrop-blur-sm
        border border-slate-700
        flex items-center justify-center
        shadow-lg
      "
    >
      <Moon className="h-5 w-5 text-amber-400" />
    </div>
  );
}
