'use client';

import { useState, useEffect } from 'react';
import { cleanupCookies, safeLog } from '@/lib/cookieCleanup';

/**
 * Bilingual language hook for Properties system
 * Supports Arabic (default) and English with proper RTL/LTR switching
 */
export function useSimpleLanguage() {
  const [language, setLanguage] = useState<'ar' | 'en'>('ar');

  // Initialize bilingual interface with cleanup
  useEffect(() => {
    // Clean up cookies first
    cleanupCookies();

    // Load saved language preference (Arabic as default)
    const savedLanguage = localStorage.getItem('properties-language');
    if (savedLanguage === 'en' || savedLanguage === 'ar') {
      setLanguage(savedLanguage);
    }

    safeLog('🏠 Bilingual Properties system initialized');
  }, []);

  // Update document direction and language when language changes
  useEffect(() => {
    // Save language preference
    localStorage.setItem('properties-language', language);

    // Update document properties
    document.documentElement.lang = language;
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';

    // Update CSS classes
    document.documentElement.className = language === 'ar' ? 'rtl arabic-interface' : 'ltr english-interface';

    // Set appropriate fonts
    if (language === 'ar') {
      document.body.style.fontFamily = "'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Amiri', sans-serif";
    } else {
      document.body.style.fontFamily = "'Inter', 'Segoe UI', 'Roboto', sans-serif";
    }

    safeLog(`🌐 Language switched to: ${language}`);
  }, [language]);

  const changeLanguage = (newLanguage: 'ar' | 'en') => {
    setLanguage(newLanguage);
  };

  return {
    language,
    setLanguage: changeLanguage,
    isRTL: language === 'ar',
    isArabic: language === 'ar',
    isEnglish: language === 'en',
  };
}
