'use client';

import { useState, useEffect } from 'react';
import { safeLog } from '@/lib/cookieCleanup';

/**
 * Dark mode theme hook for Properties system
 * Supports light and dark themes with persistence
 */
export function useTheme() {
  const [theme, setTheme] = useState<'light' | 'dark'>('dark'); // Default to dark mode

  // Initialize theme
  useEffect(() => {
    // Check for saved theme preference, default to dark mode
    const savedTheme = localStorage.getItem('properties-theme');

    if (savedTheme === 'light' || savedTheme === 'dark') {
      setTheme(savedTheme);
    } else {
      // Default to dark mode if no preference saved
      setTheme('dark');
      localStorage.setItem('properties-theme', 'dark');
    }

    // Set initial dark class
    document.documentElement.classList.add('dark');

    safeLog('🎨 Theme system initialized (default: dark)');
  }, []);

  // Update document theme when theme changes
  useEffect(() => {
    // Save theme preference
    localStorage.setItem('properties-theme', theme);

    // Update document class
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    safeLog(`🌙 Theme switched to: ${theme}`);
  }, [theme]);

  const toggleTheme = () => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light');
  };

  const setLightTheme = () => setTheme('light');
  const setDarkTheme = () => setTheme('dark');

  return {
    theme,
    setTheme,
    toggleTheme,
    setLightTheme,
    setDarkTheme,
    isDark: theme === 'dark',
    isLight: theme === 'light',
  };
}
